@startuml Ipc_initVirtIO_Sequence

title "Sequence Diagram for Ipc_initVirtIO Function Flow"

participant "Application" as App
box "IPC Driver (Virtio)"
participant "IPC Driver API\n(Virtio)" as IpcVirtio_IF
participant "IPC Driver Private\n(Virtio)" as Driver_IpcVirtio_Private
participant "Virtio Private" as IpcVirtio_Private
participant "Ipc Vring" as IpcVring
end box

note over IpcVirtio_IF
  Ipc_initVirtIO initializes the VirtIO registry
  and sets up VirtIO communication between processors
end note

App -> IpcVirtio_IF: Ipc_initVirtIO()
note right IpcVirtio_IF
  Initailize the Virtio module.
  ◆Args: Ipc_VirtIoParams (Structure for creating VirtIO table for each core combinations)
  - vqObjBaseAddr: Base address for VirtIO objects
  - vqBufSize: Size of buffer for VirtIO objects
  - vringBaseAddr: Base address for VRings
  - vringBufSize: Size of VRing buffer
  - timeoutCnt: Timeout count for operations
  ◆Returns: #IPC_SOK on success, #IPC_EFAIL on failure
end note

activate IpcVirtio_IF

    note right IpcVirtio_IF: Initialize the array of Virtio_Object.\nRuntime object that implements a VirtIO queue for communication between cores.

    IpcVirtio_IF -> Driver_IpcVirtio_Private: VirtioIPC_init()
    note right
      VirtioIPC_init initializes VirtIO for all remote processors
      ◆Args: Ipc_VirtIoParams
      ◆Returns: #IPC_SOK on success, #IPC_EFAIL on failure
    end note
    activate Driver_IpcVirtio_Private

    loop for each remote processor

      note right Driver_IpcVirtio_Private
        Initailize Ipc_VirtioInfo which configuration structure that holds
        parameters needed to set up VirtIO communication between two cores.
      end note

      Driver_IpcVirtio_Private -> IpcVirtio_Private++: Ipc_updateVirtioInfo()
      note left IpcVirtio_Private
        Calculate addresses for TX/RX VirtIO queues
        ◆Args:
        - [in] numProc: Maximum numb er of processors (IPC_MAX_PROCS)
        - [in] baseAddr: Base address for VRings (vqParams->vringBaseAddr)
        - [in] vrBufSize: Size of VRing buffer (IPC_VRING_SIZE)
        - [out] Ipc_VirtioInfo: Pointer to Ipc_VirtioInfo structure to be updated
        ◆Returns: None
      end note

      IpcVirtio_Private --> Driver_IpcVirtio_Private--: Return
      Driver_IpcVirtio_Private -> IpcVirtio_Private: VirtioIPC_createVirtioCorePair()
      note left IpcVirtio_Private
        Ipc_VirtioInfo is used to create 2 Virtio_Object instance (TX and RX)
        for communication between cores.
        ◆Args:
        - Ipc_VirtioInfo: Pointer to Ipc_VirtioInfo structure with VirtIO configuration
        - timeoutCnt: Timeout count for operations
        ◆Returns: #IPC_SOK on success, #IPC_EFAIL on failure
      end note
      activate IpcVirtio_Private

      note right IpcVirtio_Private
        Firtly, create TX Virtio_Object
      end note

      IpcVirtio_Private -> IpcVirtio_Private: Virtio_create()
      note right IpcVirtio_Private
        ◆Args:
        - vqId: VirtIO queue ID (vqInfo->txNotifyId)
        - procId: Remote processor ID (vqInfo->remoteId)
        - callback: Callback function (NULL for initial creation)
        - params: Pointer to Vring_Params structure
        - direction: Direction of VirtIO (VIRTIO_TX for transmit)
        - status: Initial status (0)
        - timeoutCnt: Timeout count for operations
        ◆Returns: Handle to the created Virtio_Object
        ◆Procedure:
        - Allocate then initialize Virtio_Object fields
        - Initialize VRing using vring_init
        - Clear TX vring memory
        - Set VRING_USED_F_NO_NOTIFY flag
        - Store Virtio_Object in queueRegistry
      end note
      activate IpcVirtio_Private #LightBlue

      IpcVirtio_Private --> IpcVirtio_Private: Return tx_vq
      deactivate IpcVirtio_Private

      note right IpcVirtio_Private
        Then, prime the transmit buffer
      end note

      deactivate IpcVirtio_IF

      Driver_IpcVirtio_Private -> IpcVring: vring_init(&(vq->vring), params->num, (void*)params->addr, params->align)
      note right
        Arguments:
        - vr: Pointer to VRing structure to initialize
        - num: Number of buffers in the ring
        - p: Base address for the ring
        - pagesize: Alignment for the ring
      end note
      activate IpcVring

      note over IpcVring #LightYellow
        vring_init initializes the VRing structure with
        descriptor, available, and used rings
      end note

      IpcVring -> IpcVring: Set up vring descriptor, avail and used pointers
      IpcVring --> Driver_IpcVirtio_Private: Return

      deactivate IpcVring

      alt if direction == VIRTIO_TX
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: memset((void*)params->addr, 0, vring_size(params->num, params->align))
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: vq->vring.used->flags |= VRING_USED_F_NO_NOTIFY
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: queueRegistry[2U*procId] = vq
      else if direction == VIRTIO_RX
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: queueRegistry[(2U*procId)+1U] = vq
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: Ipc_mailboxRegister(selfId, procId, Virtio_isr, procId, timeoutCnt)
          note right
            Arguments:
            - selfId: Local processor ID
            - procId: Remote processor ID
            - callback: ISR callback function (Virtio_isr)
            - arg: Argument to pass to callback (procId)
            - timeoutCnt: Timeout count for operations
          end note
      end

      Driver_IpcVirtio_Private --> Driver_IpcVirtio_Private: Return tx_vq
      deactivate Driver_IpcVirtio_Private

      alt if tx_vq == NULL
          Driver_IpcVirtio_Private --> Driver_IpcVirtio_Private: return IPC_EFAIL
      else
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: primeBuf = (uint32_t *)mapPAtoVA(vqInfo->primeBuf)

          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: Virtio_prime(tx_vq, (uintptr_t)primeBuf, vqInfo->num)
          note right
            Arguments:
            - vq: Virtio handle for the TX queue
            - addr: Base address for the buffer (primeBuf)
            - num: Number of buffers to add to the ring
          end note
          activate Driver_IpcVirtio_Private #LightCoral

          note over Driver_IpcVirtio_Private #LightYellow
            Virtio_prime sets up vring buffers by adding them
            to the vring's available ring
          end note

          loop for each buffer (i = 0 to num-1)
              Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: Virtio_addAvailBuf(vq, buf, i)
              note right
                Arguments:
                - vq: Virtio handle
                - buf: Buffer to add to available ring
                - i: Index/token for the buffer
              end note
          end

          Driver_IpcVirtio_Private --> Driver_IpcVirtio_Private: Return
          deactivate Driver_IpcVirtio_Private

          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: params.num = vqInfo->num
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: params.addr = (uintptr_t)mapPAtoVA(vqInfo->daRx)
          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: params.align = vqInfo->align

          Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: Virtio_create(vqInfo->rxNotifyId, vqInfo->remoteId, NULL, &params, VIRTIO_RX, status, timeoutCnt)
          note right
            Arguments:
            - vqId: VirtIO queue ID (vqInfo->rxNotifyId)
            - procId: Remote processor ID (vqInfo->remoteId)
            - callback: Callback function (NULL for initial creation)
            - params: Pointer to Vring_Params structure
            - direction: Direction of VirtIO (VIRTIO_RX for receive)
            - status: Initial status (0)
            - timeoutCnt: Timeout count for operations
          end note

          alt if rx_vq == NULL
              Driver_IpcVirtio_Private --> Driver_IpcVirtio_Private: return IPC_EFAIL
          end
      end

            Driver_IpcVirtio_Private --> Driver_IpcVirtio_Private: Return retVal
            deactivate Driver_IpcVirtio_Private

            alt if retVal != IPC_SOK
                Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: Print error message
                Driver_IpcVirtio_Private -> Driver_IpcVirtio_Private: break loop
            end


    Driver_IpcVirtio_Private --> IpcVirtio_IF: Return retVal
    deactivate Driver_IpcVirtio_Private
end

IpcVirtio_IF --> App: Return retVal

deactivate IpcVirtio_IF

@enduml
