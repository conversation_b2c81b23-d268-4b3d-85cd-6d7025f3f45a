/*
 * Copyright (C) 2018-2024 Texas Instruments Incorporated - http://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the
 * distribution.
 *
 * Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef SBL_SOC_CFG_H
#define SBL_SOC_CFG_H

/* ========================================================================== */
/*                             Include Files                                  */
/* ========================================================================== */
#include <ti/csl/soc.h>
#include <ti/csl/csl_vtm.h>
#include <ti/drv/sciclient/sciclient.h>

#define SBL_HSM_IMG_NOT_FOUND              (((int32_t)(-2)))
#define SBL_OCMC_MEMORY_TO_LOAD_HSM_BINARY (0x41C90000U)
#define SBL_HSM_IMG_MAX_SIZE               (0x40000)

#if defined (SOC_J721E)
#include <ti/csl/soc/j721e/src/cslr_wkup_ctrl_mmr.h>
#include <ti/csl/soc/j721e/src/cslr_mcu_pll_mmr.h>
#endif

#if defined (SOC_J7200)
#include <ti/csl/soc/j7200/src/cslr_wkup_ctrl_mmr.h>
#include <ti/csl/soc/j7200/src/cslr_mcu_pll_mmr.h>
#endif

#if defined (SOC_J721S2)
#include <ti/csl/soc/j721s2/src/cslr_wkup_ctrl_mmr.h>
#include <ti/csl/soc/j721s2/src/cslr_mcu_pll_mmr.h>
#endif

#if defined (SOC_J784S4) || defined (SOC_J742S2)
#include <ti/csl/soc/j784s4/src/cslr_wkup_ctrl_mmr.h>
#include <ti/csl/soc/j784s4/src/cslr_mcu_pll_mmr.h>
#endif

/* ========================================================================== */
/*                           Macros & Typedefs                                */
/* ========================================================================== */
/* Structure holding information about a core that is needed to reset it. */
typedef struct
{
    /* Proc id of a core. */
    uint32_t    region_size;
    /* Device id of a core. */
    uint32_t    local_Addr;
    /* Clk id of a core. */
    uint32_t    soc_addr_lo;
    /* Startup freq of a core in Hz */
    uint32_t    soc_addr_hi;

}sblRatCfgInfo_t;

extern sblRatCfgInfo_t* sblRatCfgList;

/* Structure holding information about the Leo PMIC configuration*/
typedef struct
{
    /* I2C slave address of the PMIC. */
    uint8_t    slaveAddr;
    /* ID of the power resource. */
    uint8_t    powerResource;
    /* Voltage associated with resource in millivolts. */
    uint16_t    millivolts;

}sblCfgPmic_t;

extern uint16_t sblMapOtpVidToMilliVolts[256];

#define SBL_MAX_VTM_VDS        (8U)
#define SBL_OPP_LOW            (CSL_VTM_CFG1_OPPVID_OPP_LOW_DFLT_SHIFT)
#define SBL_OPP_NOM            (CSL_VTM_CFG1_OPPVID_OPP_NOM_DFLT_SHIFT)
#define SBL_OPP_ODR            (CSL_VTM_CFG1_OPPVID_OPP_ODR_DFLT_SHIFT)
#define SBL_OPP_TRB            (CSL_VTM_CFG1_OPPVID_OPP_TRB_DFLT_SHIFT)

#if defined (SOC_J721E)

#define SBL_MCU_ATCM_BASE      (CSL_MCU_ARMSS_ATCM_BASE)
#define SBL_MCU_ATCM_SIZE      (CSL_MCU_ARMSS_ATCM_SIZE)
#define SBL_MCU_BTCM_BASE      (CSL_MCU_ARMSS_BTCM_BASE)
#define SBL_MCU_BTCM_SIZE      (CSL_MCU_ARMSS_BTCM_SIZE)

#define SBL_MCU1_CPU0_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU1_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU1_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU2_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU2_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_ATCM_BASE)
#define SBL_MCU3_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_ATCM_BASE)
#define SBL_MCU3_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_ATCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_SIZE             (SBL_INVALID_ID)

#define SBL_MCU1_CPU0_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU1_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU1_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU2_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU2_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_BTCM_BASE)
#define SBL_MCU3_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_BTCM_BASE)
#define SBL_MCU3_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_BTCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_SIZE             (SBL_INVALID_ID)

#define SBL_C66X_L2SRAM_BASE                (CSL_C66_COREPAC_L2_BASE)
#define SBL_C66X_L2SRAM_SIZE                (CSL_C66_COREPAC_L2_SIZE)
#define SBL_C66X_L1DMEM_BASE                (CSL_C66_COREPAC_L1D_BASE)
#define SBL_C66X_L1DMEM_SIZE                (CSL_C66_COREPAC_L1D_BASE)

#define SBL_C66X1_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C66X1_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C7X_L2SRAM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L2SRAM_SIZE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_SIZE                 (SBL_INVALID_ID)

#define SBL_C7X1_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_C7X1_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_IRAM_SIZE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_SIZE                   (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)

#define SBL_HSM_M4F_SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_HSM_M4F_SRAM_SIZE               (SBL_INVALID_ID)

#define SBL_UART_PLL_BASE                   (CSL_MCU_PLL0_CFG_BASE)
#define SBL_UART_PLL_KICK0_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY0)
#define SBL_UART_PLL_KICK1_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY1)
#define SBL_UART_PLL_DIV_OFFSET             (CSL_MCU_PLL_MMR_CFG_PLL1_HSDIV_CTRL3)
#define SBL_UART_PLL_DIV_VAL                (0x00008031)
#define SBL_UART_PLL_KICK0_UNLOCK_VAL       (0x68EF3490)
#define SBL_UART_PLL_KICK1_UNLOCK_VAL       (0xD172BC5A)
#define SBL_UART_PLL_KICK_LOCK_VAL          (0x0)
#define SBL_ROM_UART_MODULE_INPUT_CLK       (48000000U)
#define SBL_SYSFW_UART_MODULE_INPUT_CLK     (96000000U)

#define SBL_MCU_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLUDEN_SHIFT)
#define SBL_MCU_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLTYPESEL_SHIFT)
#define SBL_MCU_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_RXACTIVE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_TX_DIS_SHIFT)
#define SBL_MCU_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_MUXMODE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18)

#define SBL_SYSFW_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLUDEN_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLTYPESEL_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_RXACTIVE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_TX_DIS_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_MUXMODE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56)

#define SBL_VTM_CFG_BASE             (CSL_WKUP_VTM0_MMR_VBUSP_CFG1_BASE)
#define SBL_VTM_OPP_VID_MASK         (CSL_VTM_CFG1_OPPVID_OPP_LOW_DFLT_MASK)

#define SBL_DEV_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0)
#define SBL_CLK_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0_OSPI_RCLK_CLK)

#define SBL_DEV_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1)
#define SBL_CLK_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1_OSPI_RCLK_CLK)

#define SBL_DEV_ID_RTI0             (TISCI_DEV_MCU_RTI0)
#define SBL_DEV_ID_RTI1             (TISCI_DEV_MCU_RTI1)

#define SBL_DEV_ID_MPU_CLUSTER0     (TISCI_DEV_A72SS0)
#define SBL_DEV_ID_MPU_CLUSTER1     (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU0       (SCICLIENT_PROC_ID_A72SS0_CORE0)
#define SBL_DEV_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0)
#define SBL_CLK_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_ARM_CLK_CLK)
#define SBL_MPU1_CPU0_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU1       (SCICLIENT_PROC_ID_A72SS0_CORE1)
#define SBL_DEV_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1)
#define SBL_CLK_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1_ARM_CLK_CLK)
#define SBL_MPU1_CPU1_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_MPU1_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_MPU1_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_MPU2_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_MPU2_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_MPU2_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_MPU2_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU1_CPU0       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU1_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU1_CPU1       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU1_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU0       (SCICLIENT_PROC_ID_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU2_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU1       (SCICLIENT_PROC_ID_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU2_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU0       (SCICLIENT_PROC_ID_R5FSS1_CORE0)
#define SBL_DEV_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0)
#define SBL_CLK_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0_CPU_CLK)
#define SBL_MCU3_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU1       (SCICLIENT_PROC_ID_R5FSS1_CORE1)
#define SBL_DEV_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1)
#define SBL_CLK_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1_CPU_CLK)
#define SBL_MCU3_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU4_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU4_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C66X       (SCICLIENT_PROC_ID_C66SS0_CORE0)
#define SBL_DEV_ID_DSP1_C66X        (TISCI_DEV_C66SS0_CORE0)
#define SBL_CLK_ID_DSP1_C66X        (TISCI_DEV_C66SS0_CORE0_GEM_CLKIN_CLK)
#define SBL_DSP1_C66X_FREQ_HZ       (1350000000)

#define SBL_PROC_ID_DSP2_C66X       (SCICLIENT_PROC_ID_C66SS1_CORE0)
#define SBL_DEV_ID_DSP2_C66X        (TISCI_DEV_C66SS1_CORE0)
#define SBL_CLK_ID_DSP2_C66X        (TISCI_DEV_C66SS0_CORE0_GEM_CLKIN_CLK)
#define SBL_DSP2_C66X_FREQ_HZ       (1350000000)

#define SBL_PROC_ID_DSP1_C7X        (SCICLIENT_PROC_ID_C71SS0)
#define SBL_DEV_ID_DSP1_C7X         (TISCI_DEV_C71SS0)
#define SBL_CLK_ID_DSP1_C7X         (TISCI_DEV_C71SS0_C7X_CLK)
#define SBL_DSP1_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP2_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C7X         (SBL_INVALID_ID)
#define SBL_DSP2_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP3_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_DSP3_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP4_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_DSP4_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_HSM_M4          (SBL_INVALID_ID)
#define SBL_DEV_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_CLK_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_HSM_M4_FREQ_HZ          (SBL_INVALID_ID)

#define SBL_HYPERFLASH_BASE_ADDRESS      (CSL_MCU_FSS0_DAT_REG1_BASE)
#define SBL_HYPERFLASH_CTLR_BASE_ADDRESS (CSL_MCU_FSS0_HPB_CTRL_BASE)

#endif /* if defined (SOC_J721E) */


#if defined (SOC_J7200)

#define SBL_MCU_ATCM_BASE      (CSL_MCU_R5FSS0_ATCM_BASE)
#define SBL_MCU_ATCM_SIZE      (CSL_MCU_R5FSS0_ATCM_SIZE)
#define SBL_MCU_BTCM_BASE      (CSL_MCU_R5FSS0_BTCM_BASE)
#define SBL_MCU_BTCM_SIZE      (CSL_MCU_R5FSS0_BTCM_SIZE)

#define SBL_MCU1_CPU0_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU1_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU1_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU2_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU2_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU3_CPU0_ATCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU3_CPU1_ATCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_ATCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_SIZE             (SBL_INVALID_ID)

#define SBL_MCU1_CPU0_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU1_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU1_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU2_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU2_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU3_CPU0_BTCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU3_CPU1_BTCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_BTCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_SIZE             (SBL_INVALID_ID)

#define SBL_C66X_L2SRAM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L2SRAM_SIZE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_SIZE                (SBL_INVALID_ID)

#define SBL_C66X1_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C66X1_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C7X_L2SRAM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L2SRAM_SIZE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_SIZE                 (SBL_INVALID_ID)

#define SBL_C7X1_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_C7X1_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_IRAM_SIZE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_SIZE                   (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)

#define SBL_HSM_M4F_SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_HSM_M4F_SRAM_SIZE               (SBL_INVALID_ID)

#define SBL_UART_PLL_BASE                   (CSL_MCU_PLL0_CFG_BASE)
#define SBL_UART_PLL_KICK0_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY0)
#define SBL_UART_PLL_KICK1_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY1)
#define SBL_UART_PLL_DIV_OFFSET             (CSL_MCU_PLL_MMR_CFG_PLL1_HSDIV_CTRL3)
#define SBL_UART_PLL_DIV_VAL                (0x00008031)
#define SBL_UART_PLL_KICK0_UNLOCK_VAL       (0x68EF3490)
#define SBL_UART_PLL_KICK1_UNLOCK_VAL       (0xD172BC5A)
#define SBL_UART_PLL_KICK_LOCK_VAL          (0x0)
#define SBL_ROM_UART_MODULE_INPUT_CLK       (48000000U)
#define SBL_SYSFW_UART_MODULE_INPUT_CLK     (96000000U)

#define SBL_MCU_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60_PULLUDEN_SHIFT)
#define SBL_MCU_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60_PULLTYPESEL_SHIFT)
#define SBL_MCU_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60_RXACTIVE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60_TX_DIS_SHIFT)
#define SBL_MCU_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60_MUXMODE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG60)

#define SBL_SYSFW_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45_PULLUDEN_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45_PULLTYPESEL_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45_RXACTIVE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45_TX_DIS_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45_MUXMODE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG45)

#define SBL_VTM_CFG_BASE             (CSL_WKUP_VTM0_MMR_VBUSP_CFG1_BASE)
#define SBL_VTM_OPP_VID_MASK         (CSL_VTM_CFG1_OPPVID_OPP_LOW_DFLT_MASK)

#define SBL_DEV_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0)
#define SBL_CLK_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0_OSPI_RCLK_CLK)

#define SBL_DEV_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1)
#define SBL_CLK_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1_OSPI_RCLK_CLK)

#define SBL_DEV_ID_RTI0             (TISCI_DEV_MCU_RTI0)
#define SBL_DEV_ID_RTI1             (TISCI_DEV_MCU_RTI1)

#define SBL_DEV_ID_MPU_CLUSTER0     (TISCI_DEV_A72SS0_CORE0)
#define SBL_DEV_ID_MPU_CLUSTER1     (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU0       (SCICLIENT_PROC_ID_A72SS0_CORE0)
#define SBL_DEV_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_0)
#define SBL_CLK_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_0_ARM_CLK_CLK)
#define SBL_MPU1_CPU0_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU1       (SCICLIENT_PROC_ID_A72SS0_CORE1)
#define SBL_DEV_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE0_1)
#define SBL_CLK_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE0_1_ARM_CLK_CLK)
#define SBL_MPU1_CPU1_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_MPU1_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_MPU1_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_MPU2_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_MPU2_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_MPU2_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_MPU2_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU1_CPU0       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU1_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU1_CPU1       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU1_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU0       (SCICLIENT_PROC_ID_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU2_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU1       (SCICLIENT_PROC_ID_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU2_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU3_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU3_CPU0        (SBL_INVALID_ID)
#define SBL_MCU3_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU3_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU3_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU3_CPU1        (SBL_INVALID_ID)
#define SBL_MCU3_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU4_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU4_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_DSP1_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP2_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_DSP2_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP1_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP1_C7X         (SBL_INVALID_ID)
#define SBL_DSP1_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP2_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C7X         (SBL_INVALID_ID)
#define SBL_DSP2_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP3_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_DSP3_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP4_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_DSP4_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_HSM_M4          (SBL_INVALID_ID)
#define SBL_DEV_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_CLK_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_HSM_M4_FREQ_HZ          (SBL_INVALID_ID)

#define SBL_HYPERFLASH_BASE_ADDRESS      (CSL_MCU_FSS0_DAT_REG1_BASE)
#define SBL_HYPERFLASH_CTLR_BASE_ADDRESS (CSL_MCU_FSS0_HPB_CTRL_BASE)

#endif /* if defined (SOC_J7200) */


#if defined (SOC_J721S2)

#define SBL_MCU_ATCM_BASE      (CSL_MCU_R5FSS0_ATCM_BASE)
#define SBL_MCU_ATCM_SIZE      (CSL_MCU_R5FSS0_ATCM_SIZE)
#define SBL_MCU_BTCM_BASE      (CSL_MCU_R5FSS0_BTCM_BASE)
#define SBL_MCU_BTCM_SIZE      (CSL_MCU_R5FSS0_BTCM_SIZE)

#define SBL_MCU1_CPU0_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU1_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU1_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU2_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU2_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_ATCM_BASE)
#define SBL_MCU3_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_ATCM_BASE)
#define SBL_MCU3_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_ATCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_ATCM_SIZE             (SBL_INVALID_ID)

#define SBL_MCU1_CPU0_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU1_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU1_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU2_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU2_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_BTCM_BASE)
#define SBL_MCU3_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_BTCM_BASE)
#define SBL_MCU3_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_BTCM_SIZE             (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC    (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_BTCM_SIZE             (SBL_INVALID_ID)

#define SBL_C66X_L2SRAM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L2SRAM_SIZE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_SIZE                (SBL_INVALID_ID)

#define SBL_C66X1_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C66X1_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C7X_L2SRAM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L2SRAM_SIZE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_SIZE                 (SBL_INVALID_ID)

#define SBL_C7X1_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_C7X1_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_IRAM_SIZE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_SIZE                   (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)

#define SBL_HSM_M4F_SRAM_BASE_ADDR_SOC      (CSL_WKUP_SMS0_HSM_SRAM0_0_BASE)
#define SBL_HSM_M4F_SRAM_SIZE               (CSL_WKUP_SMS0_HSM_SRAM0_0_SIZE + \
                                             CSL_WKUP_SMS0_HSM_SRAM0_1_SIZE + \
                                             CSL_WKUP_SMS0_HSM_SRAM1_SIZE)

#define SBL_UART_PLL_BASE                   (CSL_MCU_PLL0_CFG_BASE)
#define SBL_UART_PLL_KICK0_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY0)
#define SBL_UART_PLL_KICK1_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY1)
#define SBL_UART_PLL_DIV_OFFSET             (CSL_MCU_PLL_MMR_CFG_PLL1_HSDIV_CTRL3)
#define SBL_UART_PLL_DIV_VAL                (0x00008031)
#define SBL_UART_PLL_KICK0_UNLOCK_VAL       (0x68EF3490)
#define SBL_UART_PLL_KICK1_UNLOCK_VAL       (0xD172BC5A)
#define SBL_UART_PLL_KICK_LOCK_VAL          (0x0)
#define SBL_ROM_UART_MODULE_INPUT_CLK       (48000000U)
#define SBL_SYSFW_UART_MODULE_INPUT_CLK     (96000000U)

#define SBL_MCU_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLUDEN_SHIFT)
#define SBL_MCU_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLTYPESEL_SHIFT)
#define SBL_MCU_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_RXACTIVE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_TX_DIS_SHIFT)
#define SBL_MCU_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_MUXMODE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18)

#define SBL_SYSFW_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLUDEN_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLTYPESEL_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_RXACTIVE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_TX_DIS_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_MUXMODE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56)

#define SBL_VTM_CFG_BASE             (CSL_WKUP_VTM0_MMR_VBUSP_CFG1_BASE)
#define SBL_VTM_OPP_VID_MASK         (CSL_VTM_CFG1_OPPVID_OPP_LOW_DFLT_MASK)

#define SBL_DEV_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0)
#define SBL_CLK_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0_OSPI_RCLK_CLK)

#define SBL_DEV_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1)
#define SBL_CLK_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1_OSPI_RCLK_CLK)

#define SBL_DEV_ID_RTI0             (TISCI_DEV_MCU_RTI0)
#define SBL_DEV_ID_RTI1             (TISCI_DEV_MCU_RTI1)

#define SBL_DEV_ID_MPU_CLUSTER0     (TISCI_DEV_A72SS0)
#define SBL_DEV_ID_MPU_CLUSTER1     (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU0       (SCICLIENT_PROC_ID_A72SS0_CORE0)
#define SBL_DEV_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0)
#define SBL_CLK_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_ARM_CLK_CLK)
#define SBL_MPU1_CPU0_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU1       (SCICLIENT_PROC_ID_A72SS0_CORE1)
#define SBL_DEV_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1)
#define SBL_CLK_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1_ARM_CLK_CLK)
#define SBL_MPU1_CPU1_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU2        (SBL_INVALID_ID)
#define SBL_MPU1_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU1_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU1_CPU3        (SBL_INVALID_ID)
#define SBL_MPU1_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU0        (SBL_INVALID_ID)
#define SBL_MPU2_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU1        (SBL_INVALID_ID)
#define SBL_MPU2_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU2       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU2        (SBL_INVALID_ID)
#define SBL_MPU2_CPU2_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MPU2_CPU3       (SBL_INVALID_ID)
#define SBL_DEV_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_CLK_ID_MPU2_CPU3        (SBL_INVALID_ID)
#define SBL_MPU2_CPU3_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU1_CPU0       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU1_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU1_CPU1       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU1_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU0       (SCICLIENT_PROC_ID_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU2_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU1       (SCICLIENT_PROC_ID_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU2_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU0       (SCICLIENT_PROC_ID_R5FSS1_CORE0)
#define SBL_DEV_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0)
#define SBL_CLK_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0_CPU_CLK)
#define SBL_MCU3_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU1       (SCICLIENT_PROC_ID_R5FSS1_CORE1)
#define SBL_DEV_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1)
#define SBL_CLK_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1_CPU_CLK)
#define SBL_MCU3_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU4_CPU0       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU0        (SBL_INVALID_ID)
#define SBL_MCU4_CPU0_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_MCU4_CPU1       (SBL_INVALID_ID)
#define SBL_DEV_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_CLK_ID_MCU4_CPU1        (SBL_INVALID_ID)
#define SBL_MCU4_CPU1_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_DSP1_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP2_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_DSP2_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER0_C71SS0_0)
#define SBL_DEV_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0_0)
#define SBL_CLK_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0_0_C7X_CLK)
#define SBL_DSP1_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP2_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER0_C71SS1_0)
#define SBL_DEV_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1_0)
#define SBL_CLK_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1_0_C7X_CLK)
#define SBL_DSP2_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP3_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP3_C7X         (SBL_INVALID_ID)
#define SBL_DSP3_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP4_C7X        (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP4_C7X         (SBL_INVALID_ID)
#define SBL_DSP4_C7X_FREQ_HZ        (SBL_INVALID_ID)

#define SBL_PROC_ID_HSM_M4          (SCICLIENT_PROC_ID_WKUP_HSM0)
#define SBL_DEV_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_CLK_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_HSM_M4_FREQ_HZ          (SBL_INVALID_ID)

#define SBL_HYPERFLASH_BASE_ADDRESS      (CSL_MCU_FSS0_DAT_REG1_BASE)
#define SBL_HYPERFLASH_CTLR_BASE_ADDRESS (CSL_MCU_FSS0_HPB_CTRL_BASE)

#endif /* if defined (SOC_J721S2) */

#if defined (SOC_J784S4) || defined (SOC_J742S2)

#define SBL_MCU_ATCM_BASE      (CSL_MCU_R5FSS0_ATCM_BASE)
#define SBL_MCU_ATCM_SIZE      (CSL_MCU_R5FSS0_ATCM_SIZE)
#define SBL_MCU_BTCM_BASE      (CSL_MCU_R5FSS0_BTCM_BASE)
#define SBL_MCU_BTCM_SIZE      (CSL_MCU_R5FSS0_BTCM_SIZE)

#define SBL_MCU1_CPU0_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU1_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_ATCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU1_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_ATCM_BASE)
#define SBL_MCU2_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_ATCM_BASE)
#define SBL_MCU2_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_ATCM_BASE)
#define SBL_MCU3_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_ATCM_BASE)
#define SBL_MCU3_CPU1_ATCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_ATCM_BASE_ADDR_SOC    (CSL_R5FSS2_CORE0_ATCM_BASE)
#define SBL_MCU4_CPU0_ATCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU1_ATCM_BASE_ADDR_SOC    (CSL_R5FSS2_CORE1_ATCM_BASE)
#define SBL_MCU4_CPU1_ATCM_SIZE             (0x8000U)

#define SBL_MCU1_CPU0_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU1_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU1_CPU1_BTCM_BASE_ADDR_SOC    (CSL_MCU_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU1_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE0_BTCM_BASE)
#define SBL_MCU2_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU2_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS0_CORE1_BTCM_BASE)
#define SBL_MCU2_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE0_BTCM_BASE)
#define SBL_MCU3_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU3_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS1_CORE1_BTCM_BASE)
#define SBL_MCU3_CPU1_BTCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU0_BTCM_BASE_ADDR_SOC    (CSL_R5FSS2_CORE0_BTCM_BASE)
#define SBL_MCU4_CPU0_BTCM_SIZE             (0x8000U)
#define SBL_MCU4_CPU1_BTCM_BASE_ADDR_SOC    (CSL_R5FSS2_CORE1_BTCM_BASE)
#define SBL_MCU4_CPU1_BTCM_SIZE             (0x8000U)

#define SBL_C66X_L2SRAM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L2SRAM_SIZE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_BASE                (SBL_INVALID_ID)
#define SBL_C66X_L1DMEM_SIZE                (SBL_INVALID_ID)

#define SBL_C66X1_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L2SRAM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C66X1_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)
#define SBL_C66X2_L1DMEM_BASE_ADDR_SOC      (SBL_INVALID_ID)

#define SBL_C7X_L2SRAM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L2SRAM_SIZE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_BASE                 (SBL_INVALID_ID)
#define SBL_C7X_L1DMEM_SIZE                 (SBL_INVALID_ID)

#define SBL_C7X1_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L2SRAM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_C7X1_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X2_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X3_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)
#define SBL_C7X4_L1DMEM_BASE_ADDR_SOC       (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_IRAM_SIZE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE                   (SBL_INVALID_ID)
#define SBL_M4F_DRAM_SIZE                   (SBL_INVALID_ID)

#define SBL_M4F_IRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)
#define SBL_M4F_DRAM_BASE_ADDR_SOC          (SBL_INVALID_ID)

#define SBL_HSM_M4F_SRAM_BASE_ADDR_SOC      (CSL_WKUP_SMS0_HSM_SRAM0_0_BASE)
#define SBL_HSM_M4F_SRAM_SIZE               (CSL_WKUP_SMS0_HSM_SRAM0_0_SIZE + \
                                             CSL_WKUP_SMS0_HSM_SRAM0_1_SIZE + \
                                             CSL_WKUP_SMS0_HSM_SRAM1_SIZE)

#define SBL_UART_PLL_BASE                   (CSL_MCU_PLL0_CFG_BASE)
#define SBL_UART_PLL_KICK0_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY0)
#define SBL_UART_PLL_KICK1_OFFSET           (CSL_MCU_PLL_MMR_CFG_PLL1_LOCKKEY1)
#define SBL_UART_PLL_DIV_OFFSET             (CSL_MCU_PLL_MMR_CFG_PLL1_HSDIV_CTRL3)
#define SBL_UART_PLL_DIV_VAL                (0x00008031)
#define SBL_UART_PLL_KICK0_UNLOCK_VAL       (0x68EF3490)
#define SBL_UART_PLL_KICK1_UNLOCK_VAL       (0xD172BC5A)
#define SBL_UART_PLL_KICK_LOCK_VAL          (0x0)
#define SBL_ROM_UART_MODULE_INPUT_CLK       (48000000U)
#define SBL_SYSFW_UART_MODULE_INPUT_CLK     (96000000U)

#define SBL_MCU_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLUDEN_SHIFT)
#define SBL_MCU_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_PULLTYPESEL_SHIFT)
#define SBL_MCU_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_RXACTIVE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_TX_DIS_SHIFT)
#define SBL_MCU_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18_MUXMODE_SHIFT)
#define SBL_MCU_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG18)

#define SBL_SYSFW_UART_PADCONFIG_PULLUDEN_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLUDEN_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_PULLTYPESEL_SHIFT    (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_PULLTYPESEL_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_RXACTIVE_SHIFT       (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_RXACTIVE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_TX_DIS_SHIFT         (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_TX_DIS_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_MUXMODE_SHIFT        (CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56_MUXMODE_SHIFT)
#define SBL_SYSFW_UART_PADCONFIG_ADDR                 (CSL_WKUP_CTRL_MMR0_CFG0_BASE + CSL_WKUP_CTRL_MMR_CFG0_PADCONFIG56)

#define SBL_VTM_CFG_BASE             (CSL_WKUP_VTM0_MMR_VBUSP_CFG1_BASE)
#define SBL_VTM_OPP_VID_MASK         (CSL_VTM_CFG1_OPPVID_OPP_LOW_DFLT_MASK)

#define SBL_DEV_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0)
#define SBL_CLK_ID_OSPI0            (TISCI_DEV_MCU_FSS0_OSPI_0_OSPI_RCLK_CLK)

#define SBL_DEV_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1)
#define SBL_CLK_ID_OSPI1            (TISCI_DEV_MCU_FSS0_OSPI_1_OSPI_RCLK_CLK)

#define SBL_DEV_ID_RTI0             (TISCI_DEV_MCU_RTI0)
#define SBL_DEV_ID_RTI1             (TISCI_DEV_MCU_RTI1)

#define SBL_DEV_ID_MPU_CLUSTER0     (TISCI_DEV_A72SS0)
#define SBL_DEV_ID_MPU_CLUSTER1     (TISCI_DEV_A72SS1)

#define SBL_PROC_ID_MPU1_CPU0       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS0_CORE0_0)
#define SBL_DEV_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0)
#define SBL_CLK_ID_MPU1_CPU0        (TISCI_DEV_A72SS0_CORE0_ARM0_CLK_CLK)
#define SBL_MPU1_CPU0_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU1       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS0_CORE1_0)
#define SBL_DEV_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1)
#define SBL_CLK_ID_MPU1_CPU1        (TISCI_DEV_A72SS0_CORE1_ARM0_CLK_CLK)
#define SBL_MPU1_CPU1_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU2       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS0_CORE2_0)
#define SBL_DEV_ID_MPU1_CPU2        (TISCI_DEV_A72SS0_CORE2)
#define SBL_CLK_ID_MPU1_CPU2        (TISCI_DEV_A72SS0_CORE2_ARM0_CLK_CLK)
#define SBL_MPU1_CPU2_FREQ_HZ       (2000000000)

#define SBL_PROC_ID_MPU1_CPU3       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS0_CORE3_0)
#define SBL_DEV_ID_MPU1_CPU3        (TISCI_DEV_A72SS0_CORE3)
#define SBL_CLK_ID_MPU1_CPU3        (TISCI_DEV_A72SS0_CORE3_ARM0_CLK_CLK)
#define SBL_MPU1_CPU3_FREQ_HZ       (2000000000)

#if defined (SOC_J742S2)
    #define SBL_PROC_ID_MPU2_CPU0       (SBL_INVALID_ID)
    #define SBL_DEV_ID_MPU2_CPU0        (SBL_INVALID_ID)
    #define SBL_CLK_ID_MPU2_CPU0        (SBL_INVALID_ID)
    #define SBL_MPU2_CPU0_FREQ_HZ       (SBL_INVALID_ID)

    #define SBL_PROC_ID_MPU2_CPU1       (SBL_INVALID_ID)
    #define SBL_DEV_ID_MPU2_CPU1        (SBL_INVALID_ID)
    #define SBL_CLK_ID_MPU2_CPU1        (SBL_INVALID_ID)
    #define SBL_MPU2_CPU1_FREQ_HZ       (SBL_INVALID_ID)

    #define SBL_PROC_ID_MPU2_CPU2       (SBL_INVALID_ID)
    #define SBL_DEV_ID_MPU2_CPU2        (SBL_INVALID_ID)
    #define SBL_CLK_ID_MPU2_CPU2        (SBL_INVALID_ID)
    #define SBL_MPU2_CPU2_FREQ_HZ       (SBL_INVALID_ID)

    #define SBL_PROC_ID_MPU2_CPU3       (SBL_INVALID_ID)
    #define SBL_DEV_ID_MPU2_CPU3        (SBL_INVALID_ID)
    #define SBL_CLK_ID_MPU2_CPU3        (SBL_INVALID_ID)
    #define SBL_MPU2_CPU3_FREQ_HZ       (SBL_INVALID_ID)
#else
    #define SBL_PROC_ID_MPU2_CPU0       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS1_CORE0_0)
    #define SBL_DEV_ID_MPU2_CPU0        (TISCI_DEV_A72SS1_CORE0)
    #define SBL_CLK_ID_MPU2_CPU0        (TISCI_DEV_A72SS1_CORE0_ARM1_CLK_CLK)
    #define SBL_MPU2_CPU0_FREQ_HZ       (2000000000)

    #define SBL_PROC_ID_MPU2_CPU1       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS1_CORE1_0)
    #define SBL_DEV_ID_MPU2_CPU1        (TISCI_DEV_A72SS1_CORE1)
    #define SBL_CLK_ID_MPU2_CPU1        (TISCI_DEV_A72SS1_CORE1_ARM1_CLK_CLK)
    #define SBL_MPU2_CPU1_FREQ_HZ       (2000000000)

    #define SBL_PROC_ID_MPU2_CPU2       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS1_CORE2_0)
    #define SBL_DEV_ID_MPU2_CPU2        (TISCI_DEV_A72SS1_CORE2)
    #define SBL_CLK_ID_MPU2_CPU2        (TISCI_DEV_A72SS1_CORE2_ARM1_CLK_CLK)
    #define SBL_MPU2_CPU2_FREQ_HZ       (2000000000)

    #define SBL_PROC_ID_MPU2_CPU3       (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_A72SS1_CORE3_0)
    #define SBL_DEV_ID_MPU2_CPU3        (TISCI_DEV_A72SS1_CORE3)
    #define SBL_CLK_ID_MPU2_CPU3        (TISCI_DEV_A72SS1_CORE3_ARM1_CLK_CLK)
    #define SBL_MPU2_CPU3_FREQ_HZ       (2000000000)
#endif

#define SBL_PROC_ID_MCU1_CPU0       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU1_CPU0        (TISCI_DEV_MCU_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU1_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU1_CPU1       (SCICLIENT_PROC_ID_MCU_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU1_CPU1        (TISCI_DEV_MCU_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU1_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU0       (SCICLIENT_PROC_ID_R5FSS0_CORE0)
#define SBL_DEV_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0)
#define SBL_CLK_ID_MCU2_CPU0        (TISCI_DEV_R5FSS0_CORE0_CPU_CLK)
#define SBL_MCU2_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU2_CPU1       (SCICLIENT_PROC_ID_R5FSS0_CORE1)
#define SBL_DEV_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1)
#define SBL_CLK_ID_MCU2_CPU1        (TISCI_DEV_R5FSS0_CORE1_CPU_CLK)
#define SBL_MCU2_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU0       (SCICLIENT_PROC_ID_R5FSS1_CORE0)
#define SBL_DEV_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0)
#define SBL_CLK_ID_MCU3_CPU0        (TISCI_DEV_R5FSS1_CORE0_CPU_CLK)
#define SBL_MCU3_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU3_CPU1       (SCICLIENT_PROC_ID_R5FSS1_CORE1)
#define SBL_DEV_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1)
#define SBL_CLK_ID_MCU3_CPU1        (TISCI_DEV_R5FSS1_CORE1_CPU_CLK)
#define SBL_MCU3_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU4_CPU0       (SCICLIENT_PROC_ID_R5FSS2_CORE0)
#define SBL_DEV_ID_MCU4_CPU0        (TISCI_DEV_R5FSS2_CORE0)
#define SBL_CLK_ID_MCU4_CPU0        (TISCI_DEV_R5FSS2_CORE0_CPU_CLK)
#define SBL_MCU4_CPU0_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_MCU4_CPU1       (SCICLIENT_PROC_ID_R5FSS2_CORE1)
#define SBL_DEV_ID_MCU4_CPU1        (TISCI_DEV_R5FSS2_CORE1)
#define SBL_CLK_ID_MCU4_CPU1        (TISCI_DEV_R5FSS2_CORE1_CPU_CLK)
#define SBL_MCU4_CPU1_FREQ_HZ       (1000000000)

#define SBL_PROC_ID_DSP1_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP1_C66X        (SBL_INVALID_ID)
#define SBL_DSP1_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP2_C66X       (SBL_INVALID_ID)
#define SBL_DEV_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_CLK_ID_DSP2_C66X        (SBL_INVALID_ID)
#define SBL_DSP2_C66X_FREQ_HZ       (SBL_INVALID_ID)

#define SBL_PROC_ID_DSP1_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_C71SS0_CORE0_0)
#define SBL_DEV_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0)
#define SBL_CLK_ID_DSP1_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS0_C7X_CLK)
#define SBL_DSP1_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP2_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_C71SS1_CORE0_0)
#define SBL_DEV_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1)
#define SBL_CLK_ID_DSP2_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS1_C7X_CLK)
#define SBL_DSP2_C7X_FREQ_HZ        (1000000000)

#define SBL_PROC_ID_DSP3_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_C71SS2_CORE0_0)
#define SBL_DEV_ID_DSP3_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS2)
#define SBL_CLK_ID_DSP3_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS2_C7X_CLK)
#define SBL_DSP3_C7X_FREQ_HZ        (1000000000)

#if defined (SOC_J742S2)
    #define SBL_PROC_ID_DSP4_C7X        (SBL_INVALID_ID)
    #define SBL_DEV_ID_DSP4_C7X         (SBL_INVALID_ID)
    #define SBL_CLK_ID_DSP4_C7X         (SBL_INVALID_ID)
    #define SBL_DSP4_C7X_FREQ_HZ        (SBL_INVALID_ID)
#else
    #define SBL_PROC_ID_DSP4_C7X        (SCICLIENT_PROC_ID_COMPUTE_CLUSTER_J7AHP0_C71SS3_CORE0_0)
    #define SBL_DEV_ID_DSP4_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS3)
    #define SBL_CLK_ID_DSP4_C7X         (TISCI_DEV_COMPUTE_CLUSTER0_C71SS3_C7X_CLK)
    #define SBL_DSP4_C7X_FREQ_HZ        (1000000000)
#endif

#define SBL_PROC_ID_HSM_M4          (SCICLIENT_PROC_ID_WKUP_HSM0)
#define SBL_DEV_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_CLK_ID_HSM_M4           (SBL_INVALID_ID)
#define SBL_HSM_M4_FREQ_HZ          (SBL_INVALID_ID)

#define SBL_HYPERFLASH_BASE_ADDRESS      (CSL_MCU_FSS0_DAT_REG1_BASE)
#define SBL_HYPERFLASH_CTLR_BASE_ADDRESS (CSL_MCU_FSS0_HPB_CTRL_BASE)

#endif /* if defined (SOC_J784S4) || defined (SOC_J742S2) */

/* ========================================================================== */
/*                          Function Declarations                             */
/* ========================================================================== */
void SBL_RAT_Config(sblRatCfgInfo_t *remap_list);
void SBL_SocEarlyInit();
void SBL_SocLateInit(void);
#if defined(SOC_J721E) || defined(SOC_J7200) || defined(SOC_J784S4) || defined(SOC_J742S2)
void SBL_ConfigureEthernet(void);
#endif
uint32_t sblAtcmSize(void);
uint32_t sblBtcmSize(void);
#endif
