/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "J784S4_TDA4AP_TDA4VP_TDA4AH_TDA4VH_AM69x" --package "ALY" --part "Default"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * These are the peripherals and settings in this configuration
 */
const iAUXPHY1                                    = scripting.addPeripheral("AUXPHY");
iAUXPHY1.$name                                    = "MyAUXPHY";
iAUXPHY1.AUXN.$assign                             = "DP0_AUXN";
iAUXPHY1.AUXP.$assign                             = "DP0_AUXP";
const iCPSW2G1                                    = scripting.addPeripheral("CPSW2G");
iCPSW2G1.$name                                    = "MyCPSW2G";
iCPSW2G1.CLKOUT.$used                             = false;
iCPSW2G1.RMII1_CRS_DV.$used                       = false;
iCPSW2G1.MDIO0_MDC.$assign                        = "MCASP1_AXR0";
iCPSW2G1.MDIO0_MDIO.$assign                       = "MCASP1_AFSX";
iCPSW2G1.RMII_REF_CLK.$used                       = false;
iCPSW2G1.RMII1_RXD0.$used                         = false;
iCPSW2G1.RMII1_RXD1.$used                         = false;
iCPSW2G1.RMII1_RX_ER.$used                        = false;
iCPSW2G1.RMII1_TXD0.$used                         = false;
iCPSW2G1.RMII1_TXD1.$used                         = false;
iCPSW2G1.RMII1_TX_EN.$used                        = false;
iCPSW2G1.RGMII1_RD0.$assign                       = "MCASP1_ACLKX";
iCPSW2G1.RGMII1_RD1.$assign                       = "MCASP0_AXR12";
iCPSW2G1.RGMII1_RD2.$assign                       = "MCASP0_AXR13";
iCPSW2G1.RGMII1_RD3.$assign                       = "MCASP0_AXR14";
iCPSW2G1.RGMII1_RXC.$assign                       = "MCASP1_AXR3";
iCPSW2G1.RGMII1_RX_CTL.$assign                    = "MCASP0_AXR15";
iCPSW2G1.RGMII1_TD0.$assign                       = "MCASP0_AXR7";
iCPSW2G1.RGMII1_TD1.$assign                       = "MCASP0_AXR8";
iCPSW2G1.RGMII1_TD2.$assign                       = "MCASP0_AXR9";
iCPSW2G1.RGMII1_TD3.$assign                       = "MCASP0_AXR10";
iCPSW2G1.RGMII1_TXC.$assign                       = "MCASP1_AXR4";
iCPSW2G1.RGMII1_TX_CTL.$assign                    = "MCASP0_AXR11";
const iCPSW9X1                                    = scripting.addPeripheral("CPSW9X");
iCPSW9X1.$name                                    = "MyCPSW9X";
iCPSW9X1.MDIO1_MDIO.$assign                       = "MCASP2_AXR0";
iCPSW9X1.MDIO1_MDC.$assign                        = "MCASP2_AFSX";
iCPSW9X1.SGMII1_TXP0.$used                        = false;
iCPSW9X1.SGMII1_TXN0.$used                        = false;
iCPSW9X1.SGMII1_RXP0.$used                        = false;
iCPSW9X1.SGMII1_RXN0.$used                        = false;
iCPSW9X1.SGMII2_TXP0.$used                        = false;
iCPSW9X1.SGMII2_TXN0.$used                        = false;
iCPSW9X1.SGMII2_RXP0.$used                        = false;
iCPSW9X1.SGMII2_RXN0.$used                        = false;
iCPSW9X1.SGMII3_TXP0.$used                        = false;
iCPSW9X1.SGMII3_TXN0.$used                        = false;
iCPSW9X1.SGMII3_RXP0.$used                        = false;
iCPSW9X1.SGMII3_RXN0.$used                        = false;
iCPSW9X1.SGMII4_TXP0.$used                        = false;
iCPSW9X1.SGMII4_TXN0.$used                        = false;
iCPSW9X1.SGMII4_RXP0.$used                        = false;
iCPSW9X1.SGMII4_RXN0.$used                        = false;
iCPSW9X1.SGMII5_TXP0.$used                        = false;
iCPSW9X1.SGMII5_TXN0.$used                        = false;
iCPSW9X1.SGMII5_RXP0.$used                        = false;
iCPSW9X1.SGMII5_RXN0.$used                        = false;
iCPSW9X1.SGMII6_TXP0.$used                        = false;
iCPSW9X1.SGMII6_TXN0.$used                        = false;
iCPSW9X1.SGMII6_RXP0.$used                        = false;
iCPSW9X1.SGMII6_RXN0.$used                        = false;
iCPSW9X1.SGMII7_TXP0.$assign                      = "SERDES2_TX2_P";
iCPSW9X1.SGMII7_TXN0.$assign                      = "SERDES2_TX2_N";
iCPSW9X1.SGMII7_RXP0.$assign                      = "SERDES2_RX2_P";
iCPSW9X1.SGMII7_RXN0.$assign                      = "SERDES2_RX2_N";
iCPSW9X1.SGMII8_TXP0.$assign                      = "SERDES2_TX3_P";
iCPSW9X1.SGMII8_TXN0.$assign                      = "SERDES2_TX3_N";
iCPSW9X1.SGMII8_RXP0.$assign                      = "SERDES2_RX3_P";
iCPSW9X1.SGMII8_RXN0.$assign                      = "SERDES2_RX3_N";
const iCSI1                                       = scripting.addPeripheral("CSI");
iCSI1.$name                                       = "MyCSI0";
iCSI1.RXCLKN.$assign                              = "CSI0_RXCLKN";
iCSI1.RXCLKP.$assign                              = "CSI0_RXCLKP";
iCSI1.RXN0.$assign                                = "CSI0_RXN0";
iCSI1.RXN1.$assign                                = "CSI0_RXN1";
iCSI1.RXN2.$assign                                = "CSI0_RXN2";
iCSI1.RXN3.$assign                                = "CSI0_RXN3";
iCSI1.RXP0.$assign                                = "CSI0_RXP0";
iCSI1.RXP1.$assign                                = "CSI0_RXP1";
iCSI1.RXP2.$assign                                = "CSI0_RXP2";
iCSI1.RXP3.$assign                                = "CSI0_RXP3";
iCSI1.RXRCALIB.$assign                            = "CSI0_RXRCALIB";
iCSI1.TXCLKN.$used                                = false;
iCSI1.TXCLKP.$used                                = false;
iCSI1.TXN0.$used                                  = false;
iCSI1.TXN1.$used                                  = false;
iCSI1.TXN2.$used                                  = false;
iCSI1.TXN3.$used                                  = false;
iCSI1.TXP0.$used                                  = false;
iCSI1.TXP1.$used                                  = false;
iCSI1.TXP2.$used                                  = false;
iCSI1.TXP3.$used                                  = false;
const iCSI2                                       = scripting.addPeripheral("CSI");
iCSI2.$name                                       = "MyCSI1";
iCSI2.RXCLKN.$assign                              = "CSI1_RXCLKN";
iCSI2.RXCLKP.$assign                              = "CSI1_RXCLKP";
iCSI2.RXN0.$assign                                = "CSI1_RXN0";
iCSI2.RXN1.$assign                                = "CSI1_RXN1";
iCSI2.RXN2.$assign                                = "CSI1_RXN2";
iCSI2.RXN3.$assign                                = "CSI1_RXN3";
iCSI2.RXP0.$assign                                = "CSI1_RXP0";
iCSI2.RXP1.$assign                                = "CSI1_RXP1";
iCSI2.RXP2.$assign                                = "CSI1_RXP2";
iCSI2.RXP3.$assign                                = "CSI1_RXP3";
iCSI2.RXRCALIB.$assign                            = "CSI1_RXRCALIB";
iCSI2.TXCLKN.$used                                = false;
iCSI2.TXCLKP.$used                                = false;
iCSI2.TXN0.$used                                  = false;
iCSI2.TXN1.$used                                  = false;
iCSI2.TXN2.$used                                  = false;
iCSI2.TXN3.$used                                  = false;
iCSI2.TXP0.$used                                  = false;
iCSI2.TXP1.$used                                  = false;
iCSI2.TXP2.$used                                  = false;
iCSI2.TXP3.$used                                  = false;
const iCSI3                                       = scripting.addPeripheral("CSI");
iCSI3.$name                                       = "MyCSI2";
iCSI3.RXCLKN.$assign                              = "CSI2_RXCLKN";
iCSI3.RXCLKP.$assign                              = "CSI2_RXCLKP";
iCSI3.RXN0.$assign                                = "CSI2_RXN0";
iCSI3.RXN1.$assign                                = "CSI2_RXN1";
iCSI3.RXN2.$assign                                = "CSI2_RXN2";
iCSI3.RXN3.$assign                                = "CSI2_RXN3";
iCSI3.RXP0.$assign                                = "CSI2_RXP0";
iCSI3.RXP1.$assign                                = "CSI2_RXP1";
iCSI3.RXP2.$assign                                = "CSI2_RXP2";
iCSI3.RXP3.$assign                                = "CSI2_RXP3";
iCSI3.RXRCALIB.$assign                            = "CSI2_RXRCALIB";
iCSI3.TXCLKN.$used                                = false;
iCSI3.TXCLKP.$used                                = false;
iCSI3.TXN0.$used                                  = false;
iCSI3.TXN1.$used                                  = false;
iCSI3.TXN2.$used                                  = false;
iCSI3.TXN3.$used                                  = false;
iCSI3.TXP0.$used                                  = false;
iCSI3.TXP1.$used                                  = false;
iCSI3.TXP2.$used                                  = false;
iCSI3.TXP3.$used                                  = false;
const iDDRSS1                                     = scripting.addPeripheral("DDRSS");
iDDRSS1.$name                                     = "MyDDRSS0";
iDDRSS1.CA0.$assign                               = "DDR0_CA0";
iDDRSS1.CA1.$assign                               = "DDR0_CA1";
iDDRSS1.CA2.$assign                               = "DDR0_CA2";
iDDRSS1.CA3.$assign                               = "DDR0_CA3";
iDDRSS1.CA4.$assign                               = "DDR0_CA4";
iDDRSS1.CA5.$assign                               = "DDR0_CA5";
iDDRSS1.CAL0.$assign                              = "DDR0_CAL0";
iDDRSS1.CKE0.$assign                              = "DDR0_CKE0";
iDDRSS1.CKE1.$assign                              = "DDR0_CKE1";
iDDRSS1.CKN.$assign                               = "DDR0_CKN";
iDDRSS1.CKP.$assign                               = "DDR0_CKP";
iDDRSS1.CSn0_0.$assign                            = "DDR0_CSn0_0";
iDDRSS1.CSn0_1.$assign                            = "DDR0_CSn0_1";
iDDRSS1.CSn1_0.$assign                            = "DDR0_CSn1_0";
iDDRSS1.CSn1_1.$assign                            = "DDR0_CSn1_1";
iDDRSS1.DM0.$assign                               = "DDR0_DM0";
iDDRSS1.DM1.$assign                               = "DDR0_DM1";
iDDRSS1.DM2.$assign                               = "DDR0_DM2";
iDDRSS1.DM3.$assign                               = "DDR0_DM3";
iDDRSS1.DQ0.$assign                               = "DDR0_DQ0";
iDDRSS1.DQ1.$assign                               = "DDR0_DQ1";
iDDRSS1.DQ10.$assign                              = "DDR0_DQ10";
iDDRSS1.DQ11.$assign                              = "DDR0_DQ11";
iDDRSS1.DQ12.$assign                              = "DDR0_DQ12";
iDDRSS1.DQ13.$assign                              = "DDR0_DQ13";
iDDRSS1.DQ14.$assign                              = "DDR0_DQ14";
iDDRSS1.DQ15.$assign                              = "DDR0_DQ15";
iDDRSS1.DQ16.$assign                              = "DDR0_DQ16";
iDDRSS1.DQ17.$assign                              = "DDR0_DQ17";
iDDRSS1.DQ18.$assign                              = "DDR0_DQ18";
iDDRSS1.DQ19.$assign                              = "DDR0_DQ19";
iDDRSS1.DQ2.$assign                               = "DDR0_DQ2";
iDDRSS1.DQ20.$assign                              = "DDR0_DQ20";
iDDRSS1.DQ22.$assign                              = "DDR0_DQ22";
iDDRSS1.DQ23.$assign                              = "DDR0_DQ23";
iDDRSS1.DQ24.$assign                              = "DDR0_DQ24";
iDDRSS1.DQ25.$assign                              = "DDR0_DQ25";
iDDRSS1.DQ26.$assign                              = "DDR0_DQ26";
iDDRSS1.DQ27.$assign                              = "DDR0_DQ27";
iDDRSS1.DQ28.$assign                              = "DDR0_DQ28";
iDDRSS1.DQ29.$assign                              = "DDR0_DQ29";
iDDRSS1.DQ3.$assign                               = "DDR0_DQ3";
iDDRSS1.DQ30.$assign                              = "DDR0_DQ30";
iDDRSS1.DQ31.$assign                              = "DDR0_DQ31";
iDDRSS1.DQ4.$assign                               = "DDR0_DQ4";
iDDRSS1.DQ5.$assign                               = "DDR0_DQ5";
iDDRSS1.DQ6.$assign                               = "DDR0_DQ6";
iDDRSS1.DQ7.$assign                               = "DDR0_DQ7";
iDDRSS1.DQ8.$assign                               = "DDR0_DQ8";
iDDRSS1.DQ9.$assign                               = "DDR0_DQ9";
iDDRSS1.DQS0N.$assign                             = "DDR0_DQS0N";
iDDRSS1.DQS0P.$assign                             = "DDR0_DQS0P";
iDDRSS1.DQS1N.$assign                             = "DDR0_DQS1N";
iDDRSS1.DQS1P.$assign                             = "DDR0_DQS1P";
iDDRSS1.DQS2N.$assign                             = "DDR0_DQS2N";
iDDRSS1.DQS2P.$assign                             = "DDR0_DQS2P";
iDDRSS1.DQS3N.$assign                             = "DDR0_DQS3N";
iDDRSS1.DQS3P.$assign                             = "DDR0_DQS3P";
iDDRSS1.RESETn.$assign                            = "DDR0_RESETn";
iDDRSS1.RET.$assign                               = "DDR0_RET";
const iDDRSS2                                     = scripting.addPeripheral("DDRSS");
iDDRSS2.$name                                     = "MyDDRSS1";
iDDRSS2.CA0.$assign                               = "DDR1_CA0";
iDDRSS2.CA1.$assign                               = "DDR1_CA1";
iDDRSS2.CA2.$assign                               = "DDR1_CA2";
iDDRSS2.CA3.$assign                               = "DDR1_CA3";
iDDRSS2.CA4.$assign                               = "DDR1_CA4";
iDDRSS2.CA5.$assign                               = "DDR1_CA5";
iDDRSS2.CAL0.$assign                              = "DDR1_CAL0";
iDDRSS2.CKE0.$assign                              = "DDR1_CKE0";
iDDRSS2.CKE1.$assign                              = "DDR1_CKE1";
iDDRSS2.CKN.$assign                               = "DDR1_CKN";
iDDRSS2.CKP.$assign                               = "DDR1_CKP";
iDDRSS2.CSn0_0.$assign                            = "DDR1_CSn0_0";
iDDRSS2.CSn0_1.$assign                            = "DDR1_CSn0_1";
iDDRSS2.CSn1_0.$assign                            = "DDR1_CSn1_0";
iDDRSS2.CSn1_1.$assign                            = "DDR1_CSn1_1";
iDDRSS2.DM0.$assign                               = "DDR1_DM0";
iDDRSS2.DM1.$assign                               = "DDR1_DM1";
iDDRSS2.DM2.$assign                               = "DDR1_DM2";
iDDRSS2.DM3.$assign                               = "DDR1_DM3";
iDDRSS2.DQ0.$assign                               = "DDR1_DQ0";
iDDRSS2.DQ1.$assign                               = "DDR1_DQ1";
iDDRSS2.DQ10.$assign                              = "DDR1_DQ10";
iDDRSS2.DQ11.$assign                              = "DDR1_DQ11";
iDDRSS2.DQ12.$assign                              = "DDR1_DQ12";
iDDRSS2.DQ13.$assign                              = "DDR1_DQ13";
iDDRSS2.DQ14.$assign                              = "DDR1_DQ14";
iDDRSS2.DQ15.$assign                              = "DDR1_DQ15";
iDDRSS2.DQ16.$assign                              = "DDR1_DQ16";
iDDRSS2.DQ17.$assign                              = "DDR1_DQ17";
iDDRSS2.DQ18.$assign                              = "DDR1_DQ18";
iDDRSS2.DQ19.$assign                              = "DDR1_DQ19";
iDDRSS2.DQ2.$assign                               = "DDR1_DQ2";
iDDRSS2.DQ20.$assign                              = "DDR1_DQ20";
iDDRSS2.DQ21.$assign                              = "DDR1_DQ21";
iDDRSS2.DQ22.$assign                              = "DDR1_DQ22";
iDDRSS2.DQ23.$assign                              = "DDR1_DQ23";
iDDRSS2.DQ24.$assign                              = "DDR1_DQ24";
iDDRSS2.DQ25.$assign                              = "DDR1_DQ25";
iDDRSS2.DQ26.$assign                              = "DDR1_DQ26";
iDDRSS2.DQ27.$assign                              = "DDR1_DQ27";
iDDRSS2.DQ28.$assign                              = "DDR1_DQ28";
iDDRSS2.DQ29.$assign                              = "DDR1_DQ29";
iDDRSS2.DQ3.$assign                               = "DDR1_DQ3";
iDDRSS2.DQ30.$assign                              = "DDR1_DQ30";
iDDRSS2.DQ31.$assign                              = "DDR1_DQ31";
iDDRSS2.DQ4.$assign                               = "DDR1_DQ4";
iDDRSS2.DQ5.$assign                               = "DDR1_DQ5";
iDDRSS2.DQ6.$assign                               = "DDR1_DQ6";
iDDRSS2.DQ7.$assign                               = "DDR1_DQ7";
iDDRSS2.DQ8.$assign                               = "DDR1_DQ8";
iDDRSS2.DQ9.$assign                               = "DDR1_DQ9";
iDDRSS2.DQS0N.$assign                             = "DDR1_DQS0N";
iDDRSS2.DQS0P.$assign                             = "DDR1_DQS0P";
iDDRSS2.DQS1N.$assign                             = "DDR1_DQS1N";
iDDRSS2.DQS1P.$assign                             = "DDR1_DQS1P";
iDDRSS2.DQS2N.$assign                             = "DDR1_DQS2N";
iDDRSS2.DQS2P.$assign                             = "DDR1_DQS2P";
iDDRSS2.DQS3N.$assign                             = "DDR1_DQS3N";
iDDRSS2.DQS3P.$assign                             = "DDR1_DQS3P";
iDDRSS2.RESETn.$assign                            = "DDR1_RESETn";
iDDRSS2.RET.$assign                               = "DDR1_RET";
const iDDRSS3                                     = scripting.addPeripheral("DDRSS");
iDDRSS3.$name                                     = "MyDDRSS2";
iDDRSS3.CA0.$assign                               = "DDR2_CA0";
iDDRSS3.CA1.$assign                               = "DDR2_CA1";
iDDRSS3.CA2.$assign                               = "DDR2_CA2";
iDDRSS3.CA3.$assign                               = "DDR2_CA3";
iDDRSS3.CA4.$assign                               = "DDR2_CA4";
iDDRSS3.CA5.$assign                               = "DDR2_CA5";
iDDRSS3.CAL0.$assign                              = "DDR2_CAL0";
iDDRSS3.CKE0.$assign                              = "DDR2_CKE0";
iDDRSS3.CKE1.$assign                              = "DDR2_CKE1";
iDDRSS3.CKN.$assign                               = "DDR2_CKN";
iDDRSS3.CKP.$assign                               = "DDR2_CKP";
iDDRSS3.CSn0_0.$assign                            = "DDR2_CSn0_0";
iDDRSS3.CSn0_1.$assign                            = "DDR2_CSn0_1";
iDDRSS3.CSn1_0.$assign                            = "DDR2_CSn1_0";
iDDRSS3.CSn1_1.$assign                            = "DDR2_CSn1_1";
iDDRSS3.DM0.$assign                               = "DDR2_DM0";
iDDRSS3.DM1.$assign                               = "DDR2_DM1";
iDDRSS3.DM2.$assign                               = "DDR2_DM2";
iDDRSS3.DM3.$assign                               = "DDR2_DM3";
iDDRSS3.DQ0.$assign                               = "DDR2_DQ0";
iDDRSS3.DQ1.$assign                               = "DDR2_DQ1";
iDDRSS3.DQ10.$assign                              = "DDR2_DQ10";
iDDRSS3.DQ11.$assign                              = "DDR2_DQ11";
iDDRSS3.DQ12.$assign                              = "DDR2_DQ12";
iDDRSS3.DQ13.$assign                              = "DDR2_DQ13";
iDDRSS3.DQ14.$assign                              = "DDR2_DQ14";
iDDRSS3.DQ15.$assign                              = "DDR2_DQ15";
iDDRSS3.DQ16.$assign                              = "DDR2_DQ16";
iDDRSS3.DQ17.$assign                              = "DDR2_DQ17";
iDDRSS3.DQ18.$assign                              = "DDR2_DQ18";
iDDRSS3.DQ19.$assign                              = "DDR2_DQ19";
iDDRSS3.DQ2.$assign                               = "DDR2_DQ2";
iDDRSS3.DQ20.$assign                              = "DDR2_DQ20";
iDDRSS3.DQ21.$assign                              = "DDR2_DQ21";
iDDRSS3.DQ22.$assign                              = "DDR2_DQ22";
iDDRSS3.DQ23.$assign                              = "DDR2_DQ23";
iDDRSS3.DQ24.$assign                              = "DDR2_DQ24";
iDDRSS3.DQ25.$assign                              = "DDR2_DQ25";
iDDRSS3.DQ26.$assign                              = "DDR2_DQ26";
iDDRSS3.DQ27.$assign                              = "DDR2_DQ27";
iDDRSS3.DQ28.$assign                              = "DDR2_DQ28";
iDDRSS3.DQ29.$assign                              = "DDR2_DQ29";
iDDRSS3.DQ3.$assign                               = "DDR2_DQ3";
iDDRSS3.DQ30.$assign                              = "DDR2_DQ30";
iDDRSS3.DQ31.$assign                              = "DDR2_DQ31";
iDDRSS3.DQ4.$assign                               = "DDR2_DQ4";
iDDRSS3.DQ5.$assign                               = "DDR2_DQ5";
iDDRSS3.DQ6.$assign                               = "DDR2_DQ6";
iDDRSS3.DQ7.$assign                               = "DDR2_DQ7";
iDDRSS3.DQ8.$assign                               = "DDR2_DQ8";
iDDRSS3.DQ9.$assign                               = "DDR2_DQ9";
iDDRSS3.DQS0N.$assign                             = "DDR2_DQS0N";
iDDRSS3.DQS0P.$assign                             = "DDR2_DQS0P";
iDDRSS3.DQS1N.$assign                             = "DDR2_DQS1N";
iDDRSS3.DQS1P.$assign                             = "DDR2_DQS1P";
iDDRSS3.DQS2N.$assign                             = "DDR2_DQS2N";
iDDRSS3.DQS2P.$assign                             = "DDR2_DQS2P";
iDDRSS3.DQS3N.$assign                             = "DDR2_DQS3N";
iDDRSS3.DQS3P.$assign                             = "DDR2_DQS3P";
iDDRSS3.RESETn.$assign                            = "DDR2_RESETn";
iDDRSS3.RET.$assign                               = "DDR2_RET";
const iDDRSS4                                     = scripting.addPeripheral("DDRSS");
iDDRSS4.$name                                     = "MyDDRSS3";
iDDRSS4.CA0.$assign                               = "DDR3_CA0";
iDDRSS4.CA1.$assign                               = "DDR3_CA1";
iDDRSS4.CA2.$assign                               = "DDR3_CA2";
iDDRSS4.CA3.$assign                               = "DDR3_CA3";
iDDRSS4.CA4.$assign                               = "DDR3_CA4";
iDDRSS4.CA5.$assign                               = "DDR3_CA5";
iDDRSS4.CAL0.$assign                              = "DDR3_CAL0";
iDDRSS4.CKE0.$assign                              = "DDR3_CKE0";
iDDRSS4.CKE1.$assign                              = "DDR3_CKE1";
iDDRSS4.CKN.$assign                               = "DDR3_CKN";
iDDRSS4.CKP.$assign                               = "DDR3_CKP";
iDDRSS4.CSn0_0.$assign                            = "DDR3_CSn0_0";
iDDRSS4.CSn0_1.$assign                            = "DDR3_CSn0_1";
iDDRSS4.CSn1_0.$assign                            = "DDR3_CSn1_0";
iDDRSS4.CSn1_1.$assign                            = "DDR3_CSn1_1";
iDDRSS4.DM0.$assign                               = "DDR3_DM0";
iDDRSS4.DM1.$assign                               = "DDR3_DM1";
iDDRSS4.DM2.$assign                               = "DDR3_DM2";
iDDRSS4.DM3.$assign                               = "DDR3_DM3";
iDDRSS4.DQ0.$assign                               = "DDR3_DQ0";
iDDRSS4.DQ1.$assign                               = "DDR3_DQ1";
iDDRSS4.DQ10.$assign                              = "DDR3_DQ10";
iDDRSS4.DQ11.$assign                              = "DDR3_DQ11";
iDDRSS4.DQ12.$assign                              = "DDR3_DQ12";
iDDRSS4.DQ13.$assign                              = "DDR3_DQ13";
iDDRSS4.DQ14.$assign                              = "DDR3_DQ14";
iDDRSS4.DQ15.$assign                              = "DDR3_DQ15";
iDDRSS4.DQ16.$assign                              = "DDR3_DQ16";
iDDRSS4.DQ17.$assign                              = "DDR3_DQ17";
iDDRSS4.DQ18.$assign                              = "DDR3_DQ18";
iDDRSS4.DQ19.$assign                              = "DDR3_DQ19";
iDDRSS4.DQ2.$assign                               = "DDR3_DQ2";
iDDRSS4.DQ20.$assign                              = "DDR3_DQ20";
iDDRSS4.DQ21.$assign                              = "DDR3_DQ21";
iDDRSS4.DQ22.$assign                              = "DDR3_DQ22";
iDDRSS4.DQ23.$assign                              = "DDR3_DQ23";
iDDRSS4.DQ24.$assign                              = "DDR3_DQ24";
iDDRSS4.DQ25.$assign                              = "DDR3_DQ25";
iDDRSS4.DQ26.$assign                              = "DDR3_DQ26";
iDDRSS4.DQ27.$assign                              = "DDR3_DQ27";
iDDRSS4.DQ28.$assign                              = "DDR3_DQ28";
iDDRSS4.DQ29.$assign                              = "DDR3_DQ29";
iDDRSS4.DQ3.$assign                               = "DDR3_DQ3";
iDDRSS4.DQ30.$assign                              = "DDR3_DQ30";
iDDRSS4.DQ31.$assign                              = "DDR3_DQ31";
iDDRSS4.DQ4.$assign                               = "DDR3_DQ4";
iDDRSS4.DQ5.$assign                               = "DDR3_DQ5";
iDDRSS4.DQ6.$assign                               = "DDR3_DQ6";
iDDRSS4.DQ7.$assign                               = "DDR3_DQ7";
iDDRSS4.DQ8.$assign                               = "DDR3_DQ8";
iDDRSS4.DQ9.$assign                               = "DDR3_DQ9";
iDDRSS4.DQS0N.$assign                             = "DDR3_DQS0N";
iDDRSS4.DQS0P.$assign                             = "DDR3_DQS0P";
iDDRSS4.DQS1N.$assign                             = "DDR3_DQS1N";
iDDRSS4.DQS1P.$assign                             = "DDR3_DQS1P";
iDDRSS4.DQS2N.$assign                             = "DDR3_DQS2N";
iDDRSS4.DQS2P.$assign                             = "DDR3_DQS2P";
iDDRSS4.DQS3N.$assign                             = "DDR3_DQS3N";
iDDRSS4.DQS3P.$assign                             = "DDR3_DQS3P";
iDDRSS4.RESETn.$assign                            = "DDR3_RESETn";
iDDRSS4.RET.$assign                               = "DDR3_RET";
const iDP1                                        = scripting.addPeripheral("DP");
iDP1.$name                                        = "MyDP";
iDP1.HPD.$assign                                  = "SPI0_CS0";
iDP1.TXN0.$assign                                 = "SERDES4_TX0_N";
iDP1.TXN1.$assign                                 = "SERDES4_TX1_N";
iDP1.TXN2.$assign                                 = "SERDES4_TX2_N";
iDP1.TXN3.$assign                                 = "SERDES4_TX3_N";
iDP1.TXP0.$assign                                 = "SERDES4_TX0_P";
iDP1.TXP1.$assign                                 = "SERDES4_TX1_P";
iDP1.TXP2.$assign                                 = "SERDES4_TX2_P";
iDP1.TXP3.$assign                                 = "SERDES4_TX3_P";
const iDSI1                                       = scripting.addPeripheral("DSI");
iDSI1.$name                                       = "MyDSI";
iDSI1.TXCLKN.$assign                              = "DSI0_TXCLKN";
iDSI1.TXCLKP.$assign                              = "DSI0_TXCLKP";
iDSI1.TXN0.$assign                                = "DSI0_TXN0";
iDSI1.TXN1.$assign                                = "DSI0_TXN1";
iDSI1.TXN2.$assign                                = "DSI0_TXN2";
iDSI1.TXN3.$assign                                = "DSI0_TXN3";
iDSI1.TXP0.$assign                                = "DSI0_TXP0";
iDSI1.TXP1.$assign                                = "DSI0_TXP1";
iDSI1.TXP2.$assign                                = "DSI0_TXP2";
iDSI1.TXP3.$assign                                = "DSI0_TXP3";
iDSI1.TXRCALIB.$assign                            = "DSI0_TXRCALIB";
const iGPIO_VDDSHV21                              = scripting.addPeripheral("GPIO_VDDSHV2");
iGPIO_VDDSHV21["1"].$used                         = false;
iGPIO_VDDSHV21["2"].$used                         = false;
iGPIO_VDDSHV21["3"].$assign                       = "MCAN13_TX";
iGPIO_VDDSHV21["4"].$used                         = false;
iGPIO_VDDSHV21["5"].$used                         = false;
iGPIO_VDDSHV21["6"].$used                         = false;
iGPIO_VDDSHV21["7"].$used                         = false;
iGPIO_VDDSHV21["8"].$assign                       = "MCAN15_RX";
iGPIO_VDDSHV21["9"].$used                         = false;
iGPIO_VDDSHV21["10"].$used                        = false;
iGPIO_VDDSHV21["11"].$assign                      = "GPIO0_11";
iGPIO_VDDSHV21["12"].$used                        = false;
iGPIO_VDDSHV21["13"].$used                        = false;
iGPIO_VDDSHV21["14"].$used                        = false;
iGPIO_VDDSHV21["15"].$used                        = false;
iGPIO_VDDSHV21["16"].$used                        = false;
iGPIO_VDDSHV21["17"].$used                        = false;
iGPIO_VDDSHV21["18"].$assign                      = "MCASP0_AXR2";
iGPIO_VDDSHV21["19"].$used                        = false;
iGPIO_VDDSHV21["20"].$used                        = false;
iGPIO_VDDSHV21["21"].$assign                      = "MCASP2_ACLKX";
iGPIO_VDDSHV21["22"].$used                        = false;
iGPIO_VDDSHV21["23"].$used                        = false;
iGPIO_VDDSHV21["24"].$used                        = false;
iGPIO_VDDSHV21["25"].$used                        = false;
iGPIO_VDDSHV21["26"].$assign                      = "MCAN0_RX";
iGPIO_VDDSHV21["27"].$used                        = false;
iGPIO_VDDSHV21["28"].$assign                      = "MCAN1_RX";
iGPIO_VDDSHV21["29"].$used                        = false;
iGPIO_VDDSHV21["30"].$used                        = false;
iGPIO_VDDSHV21["31"].$used                        = false;
iGPIO_VDDSHV21["32"].$used                        = false;
iGPIO_VDDSHV21["33"].$used                        = false;
iGPIO_VDDSHV21["34"].$used                        = false;
iGPIO_VDDSHV21["35"].$used                        = false;
iGPIO_VDDSHV21["36"].$used                        = false;
iGPIO_VDDSHV21["37"].$used                        = false;
iGPIO_VDDSHV21["38"].$used                        = false;
iGPIO_VDDSHV21["39"].$used                        = false;
iGPIO_VDDSHV21["40"].$used                        = false;
iGPIO_VDDSHV21["41"].$used                        = false;
iGPIO_VDDSHV21["42"].$used                        = false;
iGPIO_VDDSHV21["43"].$used                        = false;
iGPIO_VDDSHV21["44"].$used                        = false;
iGPIO_VDDSHV21["45"].$used                        = false;
iGPIO_VDDSHV21["46"].$used                        = false;
iGPIO_VDDSHV21["47"].$used                        = false;
iGPIO_VDDSHV21["48"].$used                        = false;
iGPIO_VDDSHV21["49"].$used                        = false;
iGPIO_VDDSHV21["50"].$used                        = false;
iGPIO_VDDSHV21.$name                              = "MyGPIO_VDDSHV2";
const iI2C1                                       = scripting.addPeripheral("I2C");
iI2C1.$name                                       = "MyI2C4";
iI2C1.$assign                                     = "I2C_4";
iI2C1.SCL.$assign                                 = "MCAN14_TX";
iI2C1.SDA.$assign                                 = "MCAN13_RX";
const iI2C2                                       = scripting.addPeripheral("I2C");
iI2C2.$name                                       = "MyI2C5";
iI2C2.$assign                                     = "I2C_5";
iI2C2.SCL.$assign                                 = "MCAN15_TX";
iI2C2.SDA.$assign                                 = "MCAN14_RX";
const iI2C3                                       = scripting.addPeripheral("I2C");
iI2C3.$name                                       = "MyI2C3";
iI2C3.$assign                                     = "I2C_3";
iI2C3.SCL.$assign                                 = "MCAN0_TX";
iI2C3.SDA.$assign                                 = "MCASP2_AXR1";
const iI2C4                                       = scripting.addPeripheral("I2C");
iI2C4.$name                                       = "MyI2C1";
iI2C4.$assign                                     = "I2C_1";
iI2C4.SCL.$assign                                 = "ECAP0_IN_APWM_OUT";
iI2C4.SDA.$assign                                 = "EXT_REFCLK1";
const iI2C5                                       = scripting.addPeripheral("I2C");
iI2C5.$name                                       = "MyI2C0";
iI2C5.SCL.$assign                                 = "I2C0_SCL";
iI2C5.SDA.$assign                                 = "I2C0_SDA";
const iJTAG_VDDSHV01                              = scripting.addPeripheral("JTAG_VDDSHV0");
iJTAG_VDDSHV01.$name                              = "MyJTAG_VDDSHV0";
iJTAG_VDDSHV01.TDI.$assign                        = "TDI";
iJTAG_VDDSHV01.TDO.$assign                        = "TDO";
iJTAG_VDDSHV01.TMS.$assign                        = "TMS";
const iMCAN1                                      = scripting.addPeripheral("MCAN");
iMCAN1.$name                                      = "MyMCAN16";
iMCAN1.$assign                                    = "MCAN_16";
iMCAN1.RX.$assign                                 = "MCAN16_RX";
iMCAN1.TX.$assign                                 = "MCAN16_TX";
const iMCAN2                                      = scripting.addPeripheral("MCAN");
iMCAN2.$name                                      = "MyMCAN5";
iMCAN2.$assign                                    = "MCAN_5";
iMCAN2.RX.$assign                                 = "MCASP0_AFSX";
iMCAN2.TX.$assign                                 = "MCASP0_ACLKX";
const iMCAN3                                      = scripting.addPeripheral("MCAN");
iMCAN3.$name                                      = "MyMCAN3";
iMCAN3.$assign                                    = "MCAN_3";
iMCAN3.RX.$assign                                 = "MCASP0_AXR4";
iMCAN3.TX.$assign                                 = "MCASP0_AXR3";
const iMCAN4                                      = scripting.addPeripheral("MCAN");
iMCAN4.$name                                      = "MyMCAN4";
iMCAN4.$assign                                    = "MCAN_4";
iMCAN4.RX.$assign                                 = "MCASP0_AXR6";
iMCAN4.TX.$assign                                 = "MCASP0_AXR5";
const iMCU_ADC1                                   = scripting.addPeripheral("MCU_ADC");
iMCU_ADC1.$name                                   = "MyMCU_ADC0";
iMCU_ADC1.REFP.$assign                            = "MCU_ADC0_REFP";
iMCU_ADC1.REFN.$assign                            = "MCU_ADC0_REFN";
iMCU_ADC1.AIN0.$assign                            = "MCU_ADC0_AIN0";
iMCU_ADC1.AIN1.$assign                            = "MCU_ADC0_AIN1";
iMCU_ADC1.AIN2.$assign                            = "MCU_ADC0_AIN2";
iMCU_ADC1.AIN3.$assign                            = "MCU_ADC0_AIN3";
iMCU_ADC1.AIN4.$assign                            = "MCU_ADC0_AIN4";
iMCU_ADC1.AIN5.$assign                            = "MCU_ADC0_AIN5";
iMCU_ADC1.AIN6.$assign                            = "MCU_ADC0_AIN6";
iMCU_ADC1.AIN7.$assign                            = "MCU_ADC0_AIN7";
const iMCU_CPSW2G1                                = scripting.addPeripheral("MCU_CPSW2G");
iMCU_CPSW2G1.$useCase                             = "RGMII";
iMCU_CPSW2G1.$name                                = "MyMCU_CPSW2G";
const iMCU_I2C1                                   = scripting.addPeripheral("MCU_I2C");
iMCU_I2C1.$name                                   = "MyMCU_I2C0";
iMCU_I2C1.$assign                                 = "MCU_I2C_0";
iMCU_I2C1.SCL.$assign                             = "MCU_I2C0_SCL";
iMCU_I2C1.SDA.$assign                             = "MCU_I2C0_SDA";
const iMCU_I3C1                                   = scripting.addPeripheral("MCU_I3C");
iMCU_I3C1.$name                                   = "MyMCU_I3C";
iMCU_I3C1.$assign                                 = "MCU_I3C_0";
iMCU_I3C1.SCL.$assign                             = "WKUP_GPIO0_8";
iMCU_I3C1.SDA.$assign                             = "WKUP_GPIO0_9";
iMCU_I3C1.SDAPULLEN.$assign                       = "WKUP_GPIO0_11";
const iMCU_MCAN1                                  = scripting.addPeripheral("MCU_MCAN");
iMCU_MCAN1.$name                                  = "MyMCU_MCAN0";
iMCU_MCAN1.$assign                                = "MCU_MCAN_0";
iMCU_MCAN1.RX.$assign                             = "MCU_MCAN0_RX";
iMCU_MCAN1.TX.$assign                             = "MCU_MCAN0_TX";
const iMCU_MCAN2                                  = scripting.addPeripheral("MCU_MCAN");
iMCU_MCAN2.$name                                  = "MyMCU_MCAN1";
iMCU_MCAN2.$assign                                = "MCU_MCAN_1";
iMCU_MCAN2.RX.$assign                             = "WKUP_GPIO0_5";
iMCU_MCAN2.TX.$assign                             = "WKUP_GPIO0_4";
const iMCU_MDIO1                                  = scripting.addPeripheral("MCU_MDIO");
iMCU_MDIO1.$name                                  = "MyMCU_MDIO";
iMCU_MDIO1.MDC.$assign                            = "MCU_MDIO0_MDC";
iMCU_MDIO1.MDIO.$assign                           = "MCU_MDIO0_MDIO";
const iMCU_OSPI1                                  = scripting.addPeripheral("MCU_OSPI");
iMCU_OSPI1.$name                                  = "MyMCU_OSPI0";
iMCU_OSPI1.CLK.$assign                            = "MCU_OSPI0_CLK";
iMCU_OSPI1.CSn0.$assign                           = "MCU_OSPI0_CSn0";
iMCU_OSPI1.CSn1.$used                             = false;
iMCU_OSPI1.CSn2.$used                             = false;
iMCU_OSPI1.CSn3.$used                             = false;
iMCU_OSPI1.D0.$assign                             = "MCU_OSPI0_D0";
iMCU_OSPI1.D1.$assign                             = "MCU_OSPI0_D1";
iMCU_OSPI1.D2.$assign                             = "MCU_OSPI0_D2";
iMCU_OSPI1.D3.$assign                             = "MCU_OSPI0_D3";
iMCU_OSPI1.D4.$assign                             = "MCU_OSPI0_D4";
iMCU_OSPI1.D5.$assign                             = "MCU_OSPI0_D5";
iMCU_OSPI1.D6.$assign                             = "MCU_OSPI0_D6";
iMCU_OSPI1.D7.$assign                             = "MCU_OSPI0_D7";
iMCU_OSPI1.DQS.$assign                            = "MCU_OSPI0_DQS";
iMCU_OSPI1.ECC_FAIL.$assign                       = "MCU_OSPI0_CSn3";
iMCU_OSPI1.LBCLKO.$used                           = false;
iMCU_OSPI1.RESET_OUT0.$assign                     = "MCU_OSPI0_CSn2";
iMCU_OSPI1.RESET_OUT1.$used                       = false;
const iMCU_OSPI2                                  = scripting.addPeripheral("MCU_OSPI");
iMCU_OSPI2.$name                                  = "MyMCU_OSPI1";
iMCU_OSPI2.$assign                                = "MCU_OSPI_1";
iMCU_OSPI2.CLK.$assign                            = "MCU_OSPI1_CLK";
iMCU_OSPI2.CSn0.$assign                           = "MCU_OSPI1_CSn0";
iMCU_OSPI2.CSn1.$used                             = false;
iMCU_OSPI2.CSn2.$used                             = false;
iMCU_OSPI2.CSn3.$used                             = false;
iMCU_OSPI2.D0.$assign                             = "MCU_OSPI1_D0";
iMCU_OSPI2.D1.$assign                             = "MCU_OSPI1_D1";
iMCU_OSPI2.D2.$assign                             = "MCU_OSPI1_D2";
iMCU_OSPI2.D3.$assign                             = "MCU_OSPI1_D3";
iMCU_OSPI2.D4.$used                               = false;
iMCU_OSPI2.D5.$used                               = false;
iMCU_OSPI2.D6.$used                               = false;
iMCU_OSPI2.D7.$used                               = false;
iMCU_OSPI2.DQS.$assign                            = "MCU_OSPI1_DQS";
iMCU_OSPI2.ECC_FAIL.$used                         = false;
iMCU_OSPI2.LBCLKO.$assign                         = "MCU_OSPI1_LBCLKO";
iMCU_OSPI2.RESET_OUT0.$used                       = false;
iMCU_OSPI2.RESET_OUT1.$used                       = false;
const iMCU_UART1                                  = scripting.addPeripheral("MCU_UART");
iMCU_UART1.$name                                  = "MyMCU_UART0";
iMCU_UART1.$assign                                = "MCU_USART_0";
iMCU_UART1.CTSn.$assign                           = "WKUP_GPIO0_14";
iMCU_UART1.RTSn.$assign                           = "WKUP_GPIO0_15";
iMCU_UART1.RXD.$assign                            = "WKUP_GPIO0_13";
iMCU_UART1.TXD.$assign                            = "WKUP_GPIO0_12";
const iMMCSD1                                     = scripting.addPeripheral("MMCSD");
iMMCSD1.$name                                     = "MyMMCSD0";
iMMCSD1.$assign                                   = "MMC_0";
iMMCSD1.CALPAD.$assign                            = "MMC0_CALPAD";
iMMCSD1.CLK.$assign                               = "MMC0_CLK";
iMMCSD1.CMD.$assign                               = "MMC0_CMD";
iMMCSD1.DAT0.$assign                              = "MMC0_DAT0";
iMMCSD1.DAT1.$assign                              = "MMC0_DAT1";
iMMCSD1.DAT2.$assign                              = "MMC0_DAT2";
iMMCSD1.DAT3.$assign                              = "MMC0_DAT3";
iMMCSD1.DAT4.$assign                              = "MMC0_DAT4";
iMMCSD1.DAT5.$assign                              = "MMC0_DAT5";
iMMCSD1.DAT6.$assign                              = "MMC0_DAT6";
iMMCSD1.DAT7.$assign                              = "MMC0_DAT7";
iMMCSD1.DS.$assign                                = "MMC0_DS";
iMMCSD1.SDCD.$used                                = false;
iMMCSD1.SDWP.$used                                = false;
const iMMCSD2                                     = scripting.addPeripheral("MMCSD");
iMMCSD2.$name                                     = "MyMMCSD1";
iMMCSD2.$assign                                   = "MMC_1";
iMMCSD2.CALPAD.$used                              = false;
iMMCSD2.CLK.$assign                               = "MMC1_CLK";
iMMCSD2.CMD.$assign                               = "MMC1_CMD";
iMMCSD2.DAT0.$assign                              = "MMC1_DAT0";
iMMCSD2.DAT1.$assign                              = "MMC1_DAT1";
iMMCSD2.DAT2.$assign                              = "MMC1_DAT2";
iMMCSD2.DAT3.$assign                              = "MMC1_DAT3";
iMMCSD2.DAT4.$used                                = false;
iMMCSD2.DAT5.$used                                = false;
iMMCSD2.DAT6.$used                                = false;
iMMCSD2.DAT7.$used                                = false;
iMMCSD2.DS.$used                                  = false;
iMMCSD2.SDCD.$assign                              = "TIMER_IO0";
iMMCSD2.SDWP.$used                                = false;
const iOSC1                                       = scripting.addPeripheral("OSC");
iOSC1.$name                                       = "MyOSC1";
iOSC1.$assign                                     = "OSC_1";
iOSC1.XI.$assign                                  = "OSC1_XI";
iOSC1.XO.$assign                                  = "OSC1_XO";
const iPCIE1                                      = scripting.addPeripheral("PCIE");
iPCIE1.$name                                      = "MyPCIE1";
iPCIE1.$assign                                    = "PCIE_1";
iPCIE1.REFCLK_N_OUT.$assign                       = "PCIE_REFCLK1_N_OUT";
iPCIE1.CLKREQn.$used                              = false;
iPCIE1.RXN0.$assign                               = "SERDES0_RX0_N";
iPCIE1.RXN1.$assign                               = "SERDES0_RX1_N";
iPCIE1.RXN2.$used                                 = false;
iPCIE1.RXN3.$used                                 = false;
iPCIE1.RXP0.$assign                               = "SERDES0_RX0_P";
iPCIE1.RXP1.$assign                               = "SERDES0_RX1_P";
iPCIE1.RXP2.$used                                 = false;
iPCIE1.RXP3.$used                                 = false;
iPCIE1.TXN0.$assign                               = "SERDES0_TX0_N";
iPCIE1.TXN1.$assign                               = "SERDES0_TX1_N";
iPCIE1.TXN2.$used                                 = false;
iPCIE1.TXN3.$used                                 = false;
iPCIE1.TXP0.$assign                               = "SERDES0_TX0_P";
iPCIE1.TXP1.$assign                               = "SERDES0_TX1_P";
iPCIE1.TXP2.$used                                 = false;
iPCIE1.TXP3.$used                                 = false;
const iPCIE2                                      = scripting.addPeripheral("PCIE");
iPCIE2.$name                                      = "MyPCIE0";
iPCIE2.$assign                                    = "PCIE_0";
iPCIE2.REFCLK_N_OUT.$used                         = false;
iPCIE2.REFCLK_P_OUT.$used                         = false;
iPCIE2.CLKREQn.$used                              = false;
iPCIE2.RXN0.$assign                               = "SERDES1_RX0_N";
iPCIE2.RXN1.$assign                               = "SERDES1_RX1_N";
iPCIE2.RXN2.$assign                               = "SERDES1_RX2_N";
iPCIE2.RXN3.$assign                               = "SERDES1_RX3_N";
iPCIE2.RXP0.$assign                               = "SERDES1_RX0_P";
iPCIE2.RXP1.$assign                               = "SERDES1_RX1_P";
iPCIE2.RXP2.$assign                               = "SERDES1_RX2_P";
iPCIE2.RXP3.$assign                               = "SERDES1_RX3_P";
iPCIE2.TXN0.$assign                               = "SERDES1_TX0_N";
iPCIE2.TXN1.$assign                               = "SERDES1_TX1_N";
iPCIE2.TXN2.$assign                               = "SERDES1_TX2_N";
iPCIE2.TXN3.$assign                               = "SERDES1_TX3_N";
iPCIE2.TXP0.$assign                               = "SERDES1_TX0_P";
iPCIE2.TXP1.$assign                               = "SERDES1_TX1_P";
iPCIE2.TXP2.$assign                               = "SERDES1_TX2_P";
iPCIE2.TXP3.$assign                               = "SERDES1_TX3_P";
const iSERDES1                                    = scripting.addPeripheral("SERDES");
iSERDES1.$name                                    = "MySERDES1";
iSERDES1.$assign                                  = "SERDES_1";
iSERDES1.REFCLK_N.$assign                         = "SERDES1_REFCLK_N";
iSERDES1.REFCLK_P.$assign                         = "SERDES1_REFCLK_P";
iSERDES1.REXT.$assign                             = "SERDES1_REXT";
const iSERDES2                                    = scripting.addPeripheral("SERDES");
iSERDES2.$name                                    = "MySERDES0";
iSERDES2.$assign                                  = "SERDES_0";
iSERDES2.REFCLK_N.$used                           = false;
iSERDES2.REFCLK_P.$used                           = false;
iSERDES2.REXT.$assign                             = "SERDES0_REXT";
const iSERDES3                                    = scripting.addPeripheral("SERDES");
iSERDES3.$name                                    = "MySERDES2";
iSERDES3.$assign                                  = "SERDES_2";
iSERDES3.REFCLK_N.$used                           = false;
iSERDES3.REFCLK_P.$used                           = false;
iSERDES3.REXT.$assign                             = "SERDES2_REXT";
const iSERDES4                                    = scripting.addPeripheral("SERDES");
iSERDES4.$name                                    = "MySERDES4";
iSERDES4.$assign                                  = "SERDES_4";
iSERDES4.REFCLK_N.$used                           = false;
iSERDES4.REFCLK_P.$used                           = false;
iSERDES4.REXT.$assign                             = "SERDES4_REXT";
const iSYSTEM_VDDSHV01                            = scripting.addPeripheral("SYSTEM_VDDSHV0");
iSYSTEM_VDDSHV01.$name                            = "MySYSTEM_VDDSHV0";
iSYSTEM_VDDSHV01.EXTINTn.$assign                  = "EXTINTn";
iSYSTEM_VDDSHV01.OBSCLK0.$used                    = false;
iSYSTEM_VDDSHV01.RESETSTATz.$assign               = "RESETSTATz";
iSYSTEM_VDDSHV01.SOC_SAFETY_ERRORn.$assign        = "SOC_SAFETY_ERRORn";
iSYSTEM_VDDSHV01.SYSCLKOUT0.$used                 = false;
const iSYSTEM_VDDSHV21                            = scripting.addPeripheral("SYSTEM_VDDSHV2");
iSYSTEM_VDDSHV21.$name                            = "MySYSTEM_VDDSHV2";
iSYSTEM_VDDSHV21.$assign                          = "SYSTEM_0_VDDSHV2";
iSYSTEM_VDDSHV21.AUDIO_EXT_REFCLK0.$used          = false;
iSYSTEM_VDDSHV21.AUDIO_EXT_REFCLK1.$used          = false;
iSYSTEM_VDDSHV21.EXT_REFCLK1.$used                = false;
iSYSTEM_VDDSHV21.FCLK_MUX.$used                   = false;
iSYSTEM_VDDSHV21.OBSCLK1.$used                    = false;
iSYSTEM_VDDSHV21.PMIC_WAKE0n.$assign              = "PMIC_WAKE0n";
const iUART1                                      = scripting.addPeripheral("UART");
iUART1.$name                                      = "MyUART5";
iUART1.$assign                                    = "USART_5";
iUART1.CTSn.$used                                 = false;
iUART1.DCDn.$used                                 = false;
iUART1.DSRn.$used                                 = false;
iUART1.DTRn.$used                                 = false;
iUART1.RIn.$used                                  = false;
iUART1.RTSn.$used                                 = false;
iUART1.RXD.$assign                                = "MCAN12_RX";
iUART1.TXD.$assign                                = "MCAN12_TX";
const iUART2                                      = scripting.addPeripheral("UART");
iUART2.$name                                      = "MyUART9";
iUART2.$assign                                    = "USART_9";
iUART2.CTSn.$used                                 = false;
iUART2.DCDn.$used                                 = false;
iUART2.DSRn.$used                                 = false;
iUART2.DTRn.$used                                 = false;
iUART2.RIn.$used                                  = false;
iUART2.RTSn.$used                                 = false;
iUART2.RXD.$assign                                = "MCASP1_AXR1";
iUART2.TXD.$assign                                = "MCASP1_AXR2";
const iUART3                                      = scripting.addPeripheral("UART");
iUART3.$name                                      = "MyUART6";
iUART3.$assign                                    = "USART_6";
iUART3.CTSn.$used                                 = false;
iUART3.DCDn.$used                                 = false;
iUART3.DSRn.$used                                 = false;
iUART3.DTRn.$used                                 = false;
iUART3.RIn.$used                                  = false;
iUART3.RTSn.$used                                 = false;
iUART3.RXD.$assign                                = "GPIO0_12";
iUART3.TXD.$assign                                = "MCAN1_TX";
const iUART4                                      = scripting.addPeripheral("UART");
iUART4.$name                                      = "MyUART3";
iUART4.$assign                                    = "USART_3";
iUART4.CTSn.$used                                 = false;
iUART4.DCDn.$used                                 = false;
iUART4.DSRn.$used                                 = false;
iUART4.DTRn.$used                                 = false;
iUART4.RIn.$used                                  = false;
iUART4.RTSn.$used                                 = false;
iUART4.RXD.$assign                                = "MCAN2_TX";
iUART4.TXD.$assign                                = "MCAN2_RX";
const iUART5                                      = scripting.addPeripheral("UART");
iUART5.$name                                      = "MyUART2";
iUART5.$assign                                    = "USART_2";
iUART5.CTSn.$used                                 = false;
iUART5.DCDn.$used                                 = false;
iUART5.DSRn.$used                                 = false;
iUART5.DTRn.$used                                 = false;
iUART5.RIn.$used                                  = false;
iUART5.RTSn.$used                                 = false;
iUART5.RXD.$assign                                = "SPI0_D0";
iUART5.TXD.$assign                                = "SPI0_D1";
const iUART6                                      = scripting.addPeripheral("UART");
iUART6.$name                                      = "MyUART8";
iUART6.$assign                                    = "USART_8";
iUART6.CTSn.$assign                               = "MCASP0_AXR0";
iUART6.DCDn.$used                                 = false;
iUART6.DSRn.$used                                 = false;
iUART6.DTRn.$used                                 = false;
iUART6.RIn.$used                                  = false;
iUART6.RTSn.$assign                               = "MCASP0_AXR1";
iUART6.RXD.$assign                                = "SPI0_CS1";
iUART6.TXD.$assign                                = "SPI0_CLK";
const iUFS1                                       = scripting.addPeripheral("UFS");
iUFS1.$name                                       = "MyUFS";
iUFS1.TX_DP0.$assign                              = "UFS0_TX_DP0";
iUFS1.TX_DN0.$assign                              = "UFS0_TX_DN0";
iUFS1.RX_DP0.$assign                              = "UFS0_RX_DP0";
iUFS1.RX_DN0.$assign                              = "UFS0_RX_DN0";
iUFS1.TX_DP1.$assign                              = "UFS0_TX_DP1";
iUFS1.TX_DN1.$assign                              = "UFS0_TX_DN1";
iUFS1.RX_DP1.$assign                              = "UFS0_RX_DP1";
iUFS1.RX_DN1.$assign                              = "UFS0_RX_DN1";
iUFS1.RSTn.$assign                                = "UFS0_RSTn";
iUFS1.REF_CLK.$assign                             = "UFS0_REF_CLK";
const iUSB1                                       = scripting.addPeripheral("USB");
iUSB1.$name                                       = "MyUSB0";
iUSB1.$assign                                     = "USB_0";
iUSB1.DM.$assign                                  = "USB0_DM";
iUSB1.DP.$assign                                  = "USB0_DP";
iUSB1.DRVVBUS.$assign                             = "TIMER_IO1";
iUSB1.ID.$assign                                  = "USB0_ID";
iUSB1.RCALIB.$assign                              = "USB0_RCALIB";
iUSB1.SSRX1N.$assign                              = "SERDES0_RX2_N";
iUSB1.SSRX1P.$assign                              = "SERDES0_RX2_P";
iUSB1.SSRX2N.$assign                              = "SERDES0_RX3_N";
iUSB1.SSRX2P.$assign                              = "SERDES0_RX3_P";
iUSB1.SSTX1N.$assign                              = "SERDES0_TX2_N";
iUSB1.SSTX1P.$assign                              = "SERDES0_TX2_P";
iUSB1.SSTX2N.$assign                              = "SERDES0_TX3_N";
iUSB1.SSTX2P.$assign                              = "SERDES0_TX3_P";
iUSB1.VBUS.$assign                                = "USB0_VBUS";
const iWKUP_GPIO_VDDSHV0_MCU1                     = scripting.addPeripheral("WKUP_GPIO_VDDSHV0_MCU");
iWKUP_GPIO_VDDSHV0_MCU1["0"].$assign              = "WKUP_GPIO0_0";
iWKUP_GPIO_VDDSHV0_MCU1["1"].$assign              = "WKUP_GPIO0_1";
iWKUP_GPIO_VDDSHV0_MCU1["2"].$assign              = "WKUP_GPIO0_2";
iWKUP_GPIO_VDDSHV0_MCU1["3"].$assign              = "WKUP_GPIO0_3";
iWKUP_GPIO_VDDSHV0_MCU1["4"].$used                = false;
iWKUP_GPIO_VDDSHV0_MCU1["5"].$used                = false;
iWKUP_GPIO_VDDSHV0_MCU1["6"].$assign              = "WKUP_GPIO0_6";
iWKUP_GPIO_VDDSHV0_MCU1["7"].$assign              = "WKUP_GPIO0_7";
iWKUP_GPIO_VDDSHV0_MCU1["8"].$used                = false;
iWKUP_GPIO_VDDSHV0_MCU1["9"].$used                = false;
iWKUP_GPIO_VDDSHV0_MCU1["10"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["11"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["12"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["13"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["14"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["15"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["49"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["54"].$assign             = "MCU_SPI0_CLK";
iWKUP_GPIO_VDDSHV0_MCU1["55"].$assign             = "MCU_SPI0_D0";
iWKUP_GPIO_VDDSHV0_MCU1["56"].$assign             = "WKUP_GPIO0_56";
iWKUP_GPIO_VDDSHV0_MCU1["57"].$assign             = "WKUP_GPIO0_57";
iWKUP_GPIO_VDDSHV0_MCU1["58"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["59"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["60"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["61"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["63"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["64"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["65"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["66"].$assign             = "WKUP_GPIO0_66";
iWKUP_GPIO_VDDSHV0_MCU1["67"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["68"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["69"].$assign             = "MCU_SPI0_D1";
iWKUP_GPIO_VDDSHV0_MCU1["70"].$assign             = "MCU_SPI0_CS0";
iWKUP_GPIO_VDDSHV0_MCU1["87"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1["88"].$used               = false;
iWKUP_GPIO_VDDSHV0_MCU1.$name                     = "MyWKUP_GPIO_VDDSHV0_MCU";
iWKUP_GPIO_VDDSHV0_MCU1.$assign                   = "WKUP_GPIO_0_VDDSHV0_MCU";
const iWKUP_GPIO_VDDSHV1_MCU1                     = scripting.addPeripheral("WKUP_GPIO_VDDSHV1_MCU");
iWKUP_GPIO_VDDSHV1_MCU1["16"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["17"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["18"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["19"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["20"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["21"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["22"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["23"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["24"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["25"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["26"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["27"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["28"].$assign             = "MCU_OSPI0_CSn1";
iWKUP_GPIO_VDDSHV1_MCU1["29"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["30"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["31"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["32"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["33"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["34"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["35"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["36"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["37"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["38"].$used               = false;
iWKUP_GPIO_VDDSHV1_MCU1["39"].$assign             = "MCU_OSPI1_CSn1";
iWKUP_GPIO_VDDSHV1_MCU1.$name                     = "MyWKUP_GPIO_VDDSHV1_MCU";
iWKUP_GPIO_VDDSHV1_MCU1.$assign                   = "WKUP_GPIO_0_VDDSHV1_MCU";
const iWKUP_I2C1                                  = scripting.addPeripheral("WKUP_I2C");
iWKUP_I2C1.$name                                  = "MyWKUP_I2C0";
iWKUP_I2C1.SCL.$assign                            = "WKUP_I2C0_SCL";
iWKUP_I2C1.SDA.$assign                            = "WKUP_I2C0_SDA";
const iWKUP_JTAG_VDDSHV0_MCU1                     = scripting.addPeripheral("WKUP_JTAG_VDDSHV0_MCU");
iWKUP_JTAG_VDDSHV0_MCU1.$name                     = "MyWKUP_JTAG_VDDSHV0_MCU";
iWKUP_JTAG_VDDSHV0_MCU1.EMU0.$assign              = "EMU0";
iWKUP_JTAG_VDDSHV0_MCU1.EMU1.$assign              = "EMU1";
iWKUP_JTAG_VDDSHV0_MCU1.TCK.$assign               = "TCK";
iWKUP_JTAG_VDDSHV0_MCU1.TRSTn.$assign             = "TRSTn";
const iWKUP_OSC1                                  = scripting.addPeripheral("WKUP_OSC");
iWKUP_OSC1.$name                                  = "MyWKUP_OSC0";
iWKUP_OSC1.XI.$assign                             = "WKUP_OSC0_XI";
iWKUP_OSC1.XO.$assign                             = "WKUP_OSC0_XO";
const iWKUP_SYSTEM_MCU1                           = scripting.addPeripheral("WKUP_SYSTEM_MCU");
iWKUP_SYSTEM_MCU1.$name                           = "MyWKUP_SYSTEM_MCU";
iWKUP_SYSTEM_MCU1.$assign                         = "WKUP_SYSTEM_0_MCU";
iWKUP_SYSTEM_MCU1.LF_CLKIN.$assign                = "WKUP_GPIO0_67";
iWKUP_SYSTEM_MCU1.MCU_CLKOUT0.$used               = false;
iWKUP_SYSTEM_MCU1.MCU_EXT_REFCLK0.$used           = false;
iWKUP_SYSTEM_MCU1.MCU_OBSCLK0.$used               = false;
iWKUP_SYSTEM_MCU1.MCU_RESETSTATz.$assign          = "MCU_RESETSTATz";
iWKUP_SYSTEM_MCU1.MCU_RESETz.$assign              = "MCU_RESETz";
iWKUP_SYSTEM_MCU1.MCU_SYSCLKOUT0.$used            = false;
iWKUP_SYSTEM_MCU1.PMIC_POWER_EN1.$assign          = "PMIC_POWER_EN1";
iWKUP_SYSTEM_MCU1.RESET_REQz.$assign              = "RESET_REQz";
iWKUP_SYSTEM_MCU1.PMIC_WAKE1n.$assign             = "WKUP_GPIO0_49";
iWKUP_SYSTEM_MCU1.MCU_ADC_EXT_TRIGGER0.$assign    = "WKUP_GPIO0_10";
iWKUP_SYSTEM_MCU1.MCU_ADC_EXT_TRIGGER1.$used      = false;
const iWKUP_SYSTEM_VDDA_WKUP1                     = scripting.addPeripheral("WKUP_SYSTEM_VDDA_WKUP");
iWKUP_SYSTEM_VDDA_WKUP1.$name                     = "MyWKUP_SYSTEM_VDDA_WKUP";
iWKUP_SYSTEM_VDDA_WKUP1.$assign                   = "WKUP_SYSTEM_0_VDDA_WKUP";
iWKUP_SYSTEM_VDDA_WKUP1.MCU_PORz.$assign          = "MCU_PORz";
iWKUP_SYSTEM_VDDA_WKUP1.MCU_SAFETY_ERRORn.$assign = "MCU_SAFETY_ERRORn";
iWKUP_SYSTEM_VDDA_WKUP1.PORz.$assign              = "PORz";
const iWKUP_UART1                                 = scripting.addPeripheral("WKUP_UART");
iWKUP_UART1.$name                                 = "MyWKUP_UART0";
iWKUP_UART1.$assign                               = "WKUP_USART_0";
iWKUP_UART1.CTSn.$used                            = false;
iWKUP_UART1.RTSn.$used                            = false;
iWKUP_UART1.RXD.$assign                           = "WKUP_UART0_RXD";
iWKUP_UART1.TXD.$assign                           = "WKUP_UART0_TXD";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
iAUXPHY1.$suggestSolution                   = "AUXPHY_0";
iCPSW2G1.$suggestSolution                   = "CPSW2G_0";
iCPSW9X1.$suggestSolution                   = "CPSW9X_0";
iCSI1.$suggestSolution                      = "CSI_RX_0";
iCSI2.$suggestSolution                      = "CSI_RX_1";
iCSI3.$suggestSolution                      = "CSI_RX_2";
iDDRSS1.$suggestSolution                    = "DDR_0";
iDDRSS1.DQ21.$suggestSolution               = "DDR0_DQ21";
iDDRSS2.$suggestSolution                    = "DDR_1";
iDDRSS3.$suggestSolution                    = "DDR_2";
iDDRSS4.$suggestSolution                    = "DDR_3";
iDP1.$suggestSolution                       = "DP_0";
iDSI1.$suggestSolution                      = "DSI_TX_0";
iGPIO_VDDSHV21.$suggestSolution             = "GPIO_0_VDDSHV2";
iI2C5.$suggestSolution                      = "I2C_0";
iJTAG_VDDSHV01.$suggestSolution             = "JTAG_VDDSHV0";
iMCU_ADC1.$suggestSolution                  = "MCU_ADC_0";
iMCU_CPSW2G1.$suggestSolution               = "MCU_CPSW2G_0";
iMCU_CPSW2G1.RGMII1_RD0.$suggestSolution    = "MCU_RGMII1_RD0";
iMCU_CPSW2G1.RGMII1_RD1.$suggestSolution    = "MCU_RGMII1_RD1";
iMCU_CPSW2G1.RGMII1_RD2.$suggestSolution    = "MCU_RGMII1_RD2";
iMCU_CPSW2G1.RGMII1_RD3.$suggestSolution    = "MCU_RGMII1_RD3";
iMCU_CPSW2G1.RGMII1_RXC.$suggestSolution    = "MCU_RGMII1_RXC";
iMCU_CPSW2G1.RGMII1_RX_CTL.$suggestSolution = "MCU_RGMII1_RX_CTL";
iMCU_CPSW2G1.RGMII1_TD0.$suggestSolution    = "MCU_RGMII1_TD0";
iMCU_CPSW2G1.RGMII1_TD1.$suggestSolution    = "MCU_RGMII1_TD1";
iMCU_CPSW2G1.RGMII1_TD2.$suggestSolution    = "MCU_RGMII1_TD2";
iMCU_CPSW2G1.RGMII1_TD3.$suggestSolution    = "MCU_RGMII1_TD3";
iMCU_CPSW2G1.RGMII1_TXC.$suggestSolution    = "MCU_RGMII1_TXC";
iMCU_CPSW2G1.RGMII1_TX_CTL.$suggestSolution = "MCU_RGMII1_TX_CTL";
iMCU_MDIO1.$suggestSolution                 = "MCU_MDIO_0";
iMCU_OSPI1.$suggestSolution                 = "MCU_OSPI_0";
iPCIE1.REFCLK_P_OUT.$suggestSolution        = "PCIE_REFCLK1_P_OUT";
iSYSTEM_VDDSHV01.$suggestSolution           = "SYSTEM_0_VDDSHV0";
iUFS1.$suggestSolution                      = "UFS_0";
iWKUP_I2C1.$suggestSolution                 = "WKUP_I2C_0";
iWKUP_JTAG_VDDSHV0_MCU1.$suggestSolution    = "WKUP_JTAG";
iWKUP_OSC1.$suggestSolution                 = "WKUP_OSC_0";
