/******************************************************************************
 * Copyright (c) 2019 Texas Instruments Incorporated - http://www.ti.com
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *****************************************************************************/

/**
 *   \file    ds90ub92x_afe8310.h
 *
 * This file includes the structures, enums and register offsets
 * for initializing the FPD module for the communicating with the remote slave device.
 *
 */

#ifndef _DS90UB92x_AFE8310_H_ 
#define _DS90UB92x_AFE8310_H_

#include <ti/board/src/devices/common/common.h>
#include <ti/board/src/devices/fpd/ds90ub926.h>
#include <ti/board/src/devices/fpd/ds90ub925.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * \brief   Board_ub92xTunerCfg
 *
 * This function is used to configure the fpd module
 * to enable the tuner path.
 *
 * \param   handle          [IN] pointer to the i2c handle
 * \param   fpdModuleParams [IN] structure holding the device params.
 *
 * \return  BOARD_SOK in case of success or appropriate error code.
 */
Board_STATUS Board_ub92xTunerCfg(void *handle,
                                 Board_FpdModuleObj *fpdModuleParams);

/**
 * \brief   Board_ub926DesInit
 *
 * This function is used to initialize the deserializer
 * connected to host controller.
 *
 * \param   handle          [IN] pointer to the i2c handle
 * \param   fpdModuleParams [IN] structure holding the device params.
 *
 * \return  BOARD_SOK in case of success or appropriate error code.
 */
Board_STATUS Board_ub926DesInit(void *handle,
                                Board_FpdModuleObj *fpdModuleParams);

/**
 * \brief   Board_fpdUb926RmtSlvDevAccEn
 *
 * This function is used to initialize the fpd ub926
 * module to enable the access to the remote slave.
 *
 * \param   handle          [IN] pointer to the i2c handle
 * \param   fpdRmtDevParams [IN] structure holding the fpd params
 * \param   fpdModuleParams [IN] structure holding the device params.
 *
 * \return  BOARD_SOK in case of success or appropriate error code.
 */
Board_STATUS Board_fpdUb926RmtSlvDevAccEn(void *handle,
                                          Board_FpdRmtDevObj *fpdRmtDevParams,
                                          Board_FpdModuleObj *fpdModuleParams);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _DS90UB92x_AFE8310_H_ */

