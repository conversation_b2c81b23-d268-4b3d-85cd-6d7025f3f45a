[ req ]
distinguished_name = req_distinguished_name
x509_extensions = v3_ca
prompt = no
dirstring_type = nobmp

# This information will be filled by the end user.
# The current data is only a place holder.
# System firmware does not make decisions based
# on the contents of this distinguished name block.
[ req_distinguished_name ]
C = oR
ST = rx
L = gQE843yQV0sag
O = dqhGYAQ2Y4gFfCq0t1yABCYxex9eAxt71f
OU = a87RB35W
CN = x0FSqGTPWbGpuiV
emailAddress = <EMAIL>

[ v3_ca ]
basicConstraints = CA:true
*******.4.1.294.1.34  = ASN1:SEQUENCE:image_integrity

[ image_integrity ]
# Replace SEC-CERT-SHA512 with second certificate SHA512 Hash value
shaType      =  OID:2.16.840.*********.2.3
shaValue     =  FORMAT:HEX,OCT:SEC_CERT_SHA512
imageSize 	 =  INTEGER:SEC_CERT_LENGTH
