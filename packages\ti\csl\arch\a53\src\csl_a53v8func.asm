/**
    *  @file csl_arm64func.s
    *
    *  @brief
    *     A    RM General Maintainence Functions
    *  \par
    *   ================================================================================
    *
    *   @n   (C)  Copyright 2015 Texas Instruments Incorporated
    *
    *  Redistribution and use in source and binary forms, with or without
    *  modification, are permitted provided that the following conditions
    *  are met:
    *
    *      Redistributions of source code must retain the above copyright
    *      notice, this list of conditions and the following disclaimer.
    *
    *      Redistributions in binary form must reproduce the above copyright
    *      notice, this list of conditions and the following disclaimer in the
    *      documentation and/or other materials provided with the
    *      distribution.
    *
    *      Neither the name of Texas Instruments Incorporated nor the names of
    *      its contributors may be used to endorse or promote products derived
    *      from this software without specific prior written permission.
    *
    *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
    *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
    *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
    *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
    *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
    *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
    *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
    *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
    *
    *  Code derived from ARM LTD examples and documentation found in the
    *  following documents.
    *
    *  ARM® Cortex® -A53 MPCore Processor
    *  Revision: r0p4
    *  Technical Reference Manual
    *
    *  ARM® Architecture Reference Manual
    *  ARMv8, for ARMv8-A architecture profile
    *  Beta
 */


    /* */
    /* Get the contents of the ID_AA64MMFR0_EL1 register */
    /* */
    .global CSL_a53v8GetAA64MemModFeature
    .type CSL_a53v8GetAA64MemModFeature, %function
CSL_a53v8GetAA64MemModFeature:
    MRS x0, ID_AA64MMFR0_EL1
    RET

    .balign 16



    /* */
    /* Get the contents of the ID_AA64PFR0_EL1 register */
    /* */
    .global CSL_a53v8GetAA64FeatureReg0
    .type CSL_a53v8GetAA64FeatureReg0, %function
CSL_a53v8GetAA64FeatureReg0:
    MRS x0, ID_AA64PFR0_EL1
    RET

    .balign 16
