/**
 *  \file ipc_testsetup_baremetal.c
 *
 *  \brief IPC baremetal example code
 *
 */

#include <stdio.h>
#include <ti/csl/tistdtypes.h>
#include <string.h>

#include "ipc_setup.h"
#include <ti/drv/ipc/ipc.h>

/* Size of message */
#define MSGSIZE  256U
/* Service name to be registered for the end point */
#define SERVICE_PING   "ti.ipc4.ping-pong"
/* End point number to be used for IPC communication */
#define ENDPT_PING     13U
/* Service name to be registered for chrdev end point */
#define SERVICE_CHRDEV "rpmsg_chrdev"
/* End point number to be used for chrdev end point */
#define ENDPT_CHRDEV   14U
/* Number of message sent by the sender function */
#define NUMMSGS  1000U

typedef struct Ipc_TestParams_s
{
    uint32_t endPt;
    char     name[32];
} Ipc_TestParams;

Ipc_TestParams service_ping = { ENDPT_PING, SERVICE_PING };
Ipc_TestParams service_chrdev = { ENDPT_CHRDEV, SERVICE_CHRDEV };

extern uint8_t  *pCntrlBuf;
extern uint8_t  *pSendTaskBuf;
extern uint8_t  *pRecvTaskBuf;
extern uint8_t  *pSysVqBuf;

extern uint32_t  selfProcId;
extern uint32_t *pRemoteProcArray;
extern uint32_t  gNumRemoteProc;

extern RPMessage_Handle *pHandleArray;
extern uint32_t         *pEndptArray;
extern uint32_t         *pCntPing;
extern uint32_t         *pCntPong;

uint32_t gRecvTaskBufIdx = 0;

uint32_t rpmsgDataSize = RPMSG_DATA_SIZE;

void rpmsg_startSender(uint16_t dstProc, uint32_t numProc, RPMessage_Handle *handle, uint32_t *myEndPt);

volatile uint32_t gMessagesReceived = 0;

bool g_exitRespTsk = 0;

/*
 * This function can be used to stop the
 * responder task.
 */
void rpmsg_exit_responseTask()
{
    g_exitRespTsk = 1;
}

/*
 * This function waits for a "ping" message from any processor
 * then replies with a "pong" message.
 * This function also processes the "pong" messages from other
 * processors.
 */
void rpmsg_responderFxn(uintptr_t arg0, uintptr_t arg1)
{
    RPMessage_Handle handle;
    RPMessage_Handle *responseHandle = NULL;
    RPMessage_Params params;
    uint32_t         myEndPt = 0;
    uint32_t         remoteEndPt;
    uint32_t         remoteProcId;
    uint16_t         len;
    int32_t          n;
    int32_t          status = 0;
    void             *buf;
    uint32_t         lastNumMessagesReceived = 0;
    uint32_t         emptyReceiveCalls = 0;
    uint8_t          responseSent = 0;
    uint8_t          i = 0;
    uint32_t         numProc = (uint32_t)arg0;

    uint32_t         bufSize = rpmsgDataSize;
    char             str[MSGSIZE];

    buf = &pRecvTaskBuf[gRecvTaskBufIdx++ * rpmsgDataSize];
    if(buf == NULL)
    {
        App_printf("RecvTask: buffer allocation failed\n");
        return;
    }

    RPMessageParams_init(&params);

    params.requestedEndpt = service_ping.endPt;

    params.buf = buf;
    params.bufSize = bufSize;

    handle = RPMessage_create(&params, &myEndPt);
    if(!handle)
    {
        App_printf("RecvTask: Failed to create endpoint\n");
        return;
    }

    status = RPMessage_announce(RPMESSAGE_ALL, myEndPt, service_ping.name);
    if(status != IPC_SOK)
    {
        App_printf("RecvTask: RPMessage_announce() for %s failed\n", service_ping.name);
        return;
    }

    /* initialize the sender handles */
    for (i = 0; i < numProc; i++)
    {
        pHandleArray[i] = NULL;
        pCntPing[i] = 0;
        pCntPong[i] = 0;
    }

    while(!g_exitRespTsk)
    {
        App_printf("RecvTask: Waiting for messages...\n");
        /* Wait for messages to show up */
        while(gMessagesReceived == lastNumMessagesReceived);
        App_printf("RecvTask: Got %d messages\n", gMessagesReceived);

	/* Check for any new announcements first, and start the sender function */
        for (i = 0; i < numProc; i++) {
          if (pHandleArray[i] == NULL) {
            status = RPMessage_getRemoteEndPt(pRemoteProcArray[i], SERVICE_PING,
                                              &remoteProcId, &remoteEndPt, 0);
            if (status == IPC_SOK) {
              rpmsg_startSender(pRemoteProcArray[i], i, &pHandleArray[i],
                                &pEndptArray[i]);
              pCntPing[i]++;
            } else {
              App_printf("RecvTask: RPMessage_getRemoteEndPt() failed(%d)\n",
                        status);
            }
          }
        }

        /* NOTE: The following function may need to be replaced by a blocking
          function RPMessage_recv in later implementations */
        status = RPMessage_recvNb(handle,
                                  (Ptr)str, &len, &remoteEndPt,
                                  &remoteProcId);
        responseHandle = &handle;

        if (status != IPC_SOK)
        {
            /* Try the sender function handles */
            for (i = 0; i < gNumRemoteProc; i++)
            {
                if (pHandleArray[i] == NULL)
                {
                    continue;
                }

                status = RPMessage_recvNb(pHandleArray[i],
                                          (Ptr)str, &len, &remoteEndPt,
                                          &remoteProcId);
                if (status == IPC_SOK)
                {
                    break;
                }
            }
        }

        if(status != IPC_SOK)
        {
            App_printf("RecvTask: failed with code %d\n", status);

            emptyReceiveCalls++;
            lastNumMessagesReceived++;
        }
        else
        {
            lastNumMessagesReceived++;

            /* NULL terminated string */
            if (len >= MSGSIZE)
            {
                str[MSGSIZE-1] = '\0';
            }
            else
            {
                str[len] = '\0';
            }
            App_printf("RecvTask: Revcvd msg \"%s\" len %d from %s\n",
                    str, len, Ipc_mpGetName(remoteProcId));
        }
        if(status == IPC_SOK)
        {
            status = sscanf(str, "ping %d", &n);
            if(status == 1)
            {
                memset(str, 0, MSGSIZE);
                len = snprintf(str, 255, "pong %d", n);
                if(len > 255)
                {
                    App_printf("RecvTask: snprintf failed, len %d\n", len);
                    len = 255;
                }
                str[len++] = '\0';
            }
            else
            {
                status = sscanf(str, "pong %d", &n);
                if (status == 1)
                {
                    pCntPong[i]++;

                    /* This is a resopnse from a peer */
                    if (pCntPing[i] < NUMMSGS)
                    {
                        App_printf("SendTask%d: Received \"%s\" len %d from %s endPt %d \n",
                                      remoteProcId, str, len, Ipc_mpGetName(remoteProcId),
                                      remoteEndPt);
                        /* Send data to remote endPt: */
                        memset(str, 0, 256);
                        len = snprintf(str, 255, "ping %d", pCntPing[i]++);
                        if (len > 255)
                        {
                            App_printf("SendTask%d: snprintf failed, len %d\n", remoteProcId, len);
                            len = 255;
                        }
                        str[len++] = '\0';

                        App_printf("SendTask%d: Sending \"%s\" from %s to %s...\n", remoteProcId,
                                      str, Ipc_mpGetSelfName(),
                                      Ipc_mpGetName(remoteProcId));

                        status = RPMessage_send(pHandleArray[i], remoteProcId, ENDPT_PING, pEndptArray[i], (Ptr)str, len);
                        if (status != IPC_SOK)
                        {
                            App_printf("SendTask%d: RPMessage_send Failed Msg-> \"%s\" from %s to %s...\n",
                                          remoteProcId,
                                          str, Ipc_mpGetSelfName(),
                                          Ipc_mpGetName(remoteProcId));
                        }
                    }
                    else if (pCntPing[i] == NUMMSGS)
                    {
                        App_printf("%s <--> %s, Ping- %d, pong - %d completed\n",
                                      Ipc_mpGetSelfName(),
                                      Ipc_mpGetName(remoteProcId),
                                      pCntPing[i], pCntPong[i]);
                        pCntPing[i]++;
                    }
                    responseSent = 1;
                }
                else
                {
                    /* If this is not ping/pong message, just print the message */
                    App_printf("%s <--> %s : %s recvd : %d:%d:%d\n",
                                  Ipc_mpGetSelfName(),
                                  Ipc_mpGetName(remoteProcId),
                                  str,
                                  gMessagesReceived,
                                  lastNumMessagesReceived,
                                  emptyReceiveCalls);
                }
            }
            if (responseSent == 0)
            {
                App_printf("RecvTask: Sending msg \"%s\" len %d from %s to %s\n",
                              str, len, Ipc_mpGetSelfName(),
                              Ipc_mpGetName(remoteProcId));
                status = RPMessage_send(*responseHandle, remoteProcId, remoteEndPt, myEndPt, str, len);
                if (status != IPC_SOK)
                {
                    App_printf("RecvTask: Sending msg \"%s\" len %d from %s to %s failed!!!\n",
                                  str, len, Ipc_mpGetSelfName(),
                                  Ipc_mpGetName(remoteProcId));
                }
            }
            responseSent = 0;
        }
    }

    App_printf("%s responder task exiting ...\n",
                    Ipc_mpGetSelfName());
}

/*
 * This function sends a message to kick start the ping/pong messaging that will
 * repeat for predetermined number of messages. The response is handled in the responderFxn
 */
void rpmsg_startSender(uint16_t dstProc, uint32_t numProc, RPMessage_Handle *handle, uint32_t *myEndPt)
{
    RPMessage_Params    params;
    uint16_t            len;
    int32_t             status = 0;
    char                buf[256];
    uint8_t            *buf1;

    buf1 = &pSendTaskBuf[rpmsgDataSize * numProc];

    /* Create the endpoint for receiving. */
    RPMessageParams_init(&params);
    params.numBufs = 2;
    params.buf = buf1;
    params.bufSize = rpmsgDataSize;
    *handle = RPMessage_create(&params, myEndPt);
    if(!(*handle))
    {
        App_printf("SendTask %d: Failed to create message endpoint\n",
                dstProc);
        return;
    }

    /* Let's kick start the ping/pong by sending the first message.
     * We'll have to wait for response and reply together with the responderFxn.
     */

    /* Send data to remote endPt: */
    memset(buf, 0, 256);
    len = snprintf(buf, 255, "ping %d", 1);
    if (len > 255)
    {
        App_printf("SendTask%d: snprintf failed, len %d\n", dstProc, len);
        len = 255;
    }
    buf[len++] = '\0';

    App_printf("SendTask%d: Sending \"%s\" from %s to %s...\n", dstProc,
                  buf, Ipc_mpGetSelfName(),
                  Ipc_mpGetName(dstProc));

    status = RPMessage_send(*handle, dstProc, ENDPT_PING, *myEndPt, (Ptr)buf, len);
    if (status != IPC_SOK)
    {
        App_printf("SendTask%d: RPMessage_send Failed Msg-> \"%s\" from %s to %s...\n",
            dstProc,
            buf, Ipc_mpGetSelfName(),
            Ipc_mpGetName(dstProc));
    }
}

/*
 * This function is the callback function the ipc lld library calls when a
 * message is received.
 */
static void IpcTestBaremetalNewMsgCb(uint32_t srcEndPt, uint32_t procId)
{
    /* Add code here to take action on any incoming messages */
    gMessagesReceived++;
    return;
}

static void IpcTestPrint(const char *str)
{
    App_printf("%s", str);

    return;
}
uint32_t Ipc_exampleVirtToPhyFxn(const void *virtAddr)
{
    return ((uint32_t) virtAddr);
}

void *Ipc_examplePhyToVirtFxn(uint32_t phyAddr)
{
    uint32_t temp = phyAddr;

    return ((void *) temp);
}

/*
 * This is the main test function which initializes and runs the Sender and
 * responder functions.
 * NOTE: Sender function is not actually active in this example.
 *       Only the receiver function does a echo back of incoming messages
 */
/* ==========================================*/
int32_t Ipc_echo_test(void)
{
    uint32_t          numProc = gNumRemoteProc;
    Ipc_InitPrms      initPrms;
    Ipc_VirtIoParams  vqParam;

    /* Step1 : Initialize the multiproc */
    if (IPC_SOK == Ipc_mpSetConfig(selfProcId, numProc, pRemoteProcArray))
    {
        App_printf("IPC_echo_test (core : %s) .....\r\n", Ipc_mpGetSelfName());

        /* Initialize params with defaults */
        IpcInitPrms_init(0U, &initPrms);

        initPrms.newMsgFxn = &IpcTestBaremetalNewMsgCb;
        initPrms.virtToPhyFxn = &Ipc_exampleVirtToPhyFxn;
        initPrms.phyToVirtFxn = &Ipc_examplePhyToVirtFxn;
        initPrms.printFxn = &IpcTestPrint;

        if (IPC_SOK != Ipc_init(&initPrms))
        {
            App_printf("Ipc_init failed\n");
            return -1;
        }
    }
    App_printf("Required Local memory for Virtio_Object = %d\r\n",
                  numProc * Ipc_getVqObjMemoryRequiredPerCore());

    /* Step2 : Initialize Virtio */
    vqParam.vqObjBaseAddr = (void*)pSysVqBuf;
    vqParam.vqBufSize     = numProc * Ipc_getVqObjMemoryRequiredPerCore();
    vqParam.vringBaseAddr = (void*)VRING_BASE_ADDRESS;
    vqParam.vringBufSize  = IPC_VRING_BUFFER_SIZE;
    vqParam.timeoutCnt    = 100;  /* Wait for counts */
    Ipc_initVirtIO(&vqParam);

    /* Step 3: Initialize RPMessage */
    RPMessage_Params cntrlParam;

    App_printf("Required Local memory for RPMessage Object = %d\n",
                  RPMessage_getObjMemRequired());

    /* Initialize the param */
    RPMessageParams_init(&cntrlParam);

    /* Set memory for HeapMemory for control task */
    cntrlParam.buf         = pCntrlBuf;
    cntrlParam.bufSize     = rpmsgDataSize;
    cntrlParam.stackBuffer = NULL;
    cntrlParam.stackSize   = 0U;
    RPMessage_init(&cntrlParam);

    /* run responder function to receive and reply messages back */
    rpmsg_responderFxn(numProc,0);

    return 1;
}
