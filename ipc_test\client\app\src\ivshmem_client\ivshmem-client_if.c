
#include "ivshmem-client_if.h"
#include "ivshmem/common.h"
#include <atomic>
#include <errno.h>
#include <inttypes.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/select.h>
#include <ti/csl/cslr_navss_main.h>
#include <time.h>

/* Fix Me, need not be 256, can be max remote proc / max proc, i.e. 1 as
    structure which uses this is instantiated max proc times. */
#define  IPC_MBOX_MAXFIFO_CNT    16U
/* Fix Me, need not be 256, can be max remote proc / max proc */
#define  IPC_MBOX_MAXDATA        8U

typedef struct Ipc_MailboxFifo_s
{
    int32_t                 cfgNdx;
    Mailbox_hwiCallback     func;
    uint32_t                arg;
    uint32_t                queueId;
} Ipc_MailboxFifo;

/* mboxData */
typedef struct Ipc_MailboxData_s
{
    uintptr_t         baseAddr;
    uint32_t          fifoCnt;
    Ipc_MailboxFifo   fifoTable[IPC_MBOX_MAXFIFO_CNT];
    uint32_t          noMsgCnt;
    uint32_t          intCnt;
    uint32_t 	      userId;
} Ipc_MailboxData;

/* Global ivshmem client instance */
static IvshmemClient g_ivshmem_client;
static pthread_t g_ivshmem_thread;
static pthread_mutex_t g_ivshmem_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_cond_t g_ivshmem_init_cond = PTHREAD_COND_INITIALIZER;
static std::atomic_flag g_ivshmem_init_done = ATOMIC_FLAG_INIT;
static std::atomic_flag g_ivshmem_running = ATOMIC_FLAG_INIT;

extern uint32_t gMessagesReceived;
void Ipc_mailboxInternalCallback(uintptr_t arg);
extern Ipc_MailboxData g_ipc_mBoxData[IPC_MBOX_MAXDATA];

/* listen on stdin (command line), on unix socket (notifications of new
 * and dead peers), and on eventfd (IRQ request) */
static int ivshmem_cli_poll_events(IvshmemClient *client) {
  fd_set fds;
  int ret, maxfd;

  while (1) {

    FD_ZERO(&fds);
    maxfd = 0;

    ivshmem_client_get_fds(client, &fds, &maxfd);

    ret = select(maxfd, &fds, NULL, NULL, NULL);
    if (ret < 0) {
      if (errno == EINTR) {
        continue;
      }

      fprintf(stderr, "select error: %s\n", strerror(errno));
      break;
    }
    if (ret == 0) {
      continue;
    }

    if (ivshmem_client_handle_fds(client, &fds, maxfd) < 0) {
      fprintf(stderr, "ivshmem_client_handle_fds() failed\n");
      break;
    }
  }

  return ret;
}

/* callback when we receive a notification */
static void ivshmem_client_notification_cb(const IvshmemClient *client,
                                           const IvshmemClientPeer *peer,
                                           unsigned vect, void *arg) {
  (void)vect;
  (void)arg;
  uint32_t vring_msg;
  uint32_t mailbox_msg;
  uint32_t mailbox_irq_status_clr;
  uint32_t mailbox_irq_enable_set;
  uint32_t mailbox_irq_enable_clr;
  Vring_Info vring_info;
  Mailbox_Info mailbox_info;

  uint32_t self_id = peer2core_converter(client->local.id);
  uint32_t remote_id = peer2core_converter(peer->id);

  // ivshmem_client_get_mailbox_info(self_id, remote_id, &mailbox_info);
  // ivshmem_client_read_msg(mailbox_info.baseAddr, &mailbox_msg, sizeof(mailbox_msg), mailbox_info.msgOffset);
  // ivshmem_client_read_msg(mailbox_info.baseAddr, &mailbox_irq_status_clr, sizeof(mailbox_irq_status_clr), mailbox_info.irqStatusClrOffset);
  // ivshmem_client_read_msg(mailbox_info.baseAddr, &mailbox_irq_enable_set, sizeof(mailbox_irq_enable_set), mailbox_info.irqEnableSetOffset);
  // ivshmem_client_read_msg(mailbox_info.baseAddr, &mailbox_irq_enable_clr, sizeof(mailbox_irq_enable_clr), mailbox_info.irqEnableClrOffset);

  // ivshmem_client_get_vring_info(self_id, remote_id, &vring_info);
  // ivshmem_client_read_msg(VRING_BASE_ADDRESS, &vring_msg,
  //                         sizeof(vring_msg), vring_info.daRxOffset);

  // printf("receive notification from peer_id=%" PRIu64
  //        " remote_id=%u msg_mailbox=0x%08x irq_status_clr=0x%08x irq_enable_set=0x%08x irq_enable_clr=0x%08x msg_vring=%u\n",
  //        peer->id, remote_id, mailbox_msg, mailbox_irq_status_clr, mailbox_irq_enable_set, mailbox_irq_enable_clr, vring_msg);
  printf("receive notification from peer_id=%" PRIu64 " remote_id=%u\n",
         peer->id, remote_id);

  Ipc_mailboxInternalCallback((uintptr_t)&g_ipc_mBoxData[0]);
  // gMessagesReceived++;
}

static void *ivshmem_cli_thr_fn(void *arg) {
  (void)arg;
  void *ret = NULL;
  printf("ivshmem_cli_thr_fn\n");
  while (g_ivshmem_running.test_and_set(std::memory_order_acquire)) {
    if (ivshmem_client_connect(&g_ivshmem_client) < 0) {
      fprintf(stderr, "cannot connect to server, retry in 1 second\n");
      sleep(1);
      continue;
    }

    ret = mmap((void *)VRING_BASE_ADDRESS, IPC_VRING_BUFFER_SIZE,
               PROT_READ | PROT_WRITE, MAP_SHARED,
               g_ivshmem_client.vring_shm_fd, 0);
    if (ret == MAP_FAILED) {
      printf("mmap vring shared memory failed %s\n", strerror(errno));
      continue;
    }

    ret = mmap((void *)MAILBOX_BASE_ADDRESS, IPC_MAILBOX_SHM_SIZE,
               PROT_READ | PROT_WRITE, MAP_SHARED,
               g_ivshmem_client.mailbox_shm_fd, 0);
    if (ret == MAP_FAILED) {
      printf("mmap mailbox shared memory failed %s\n", strerror(errno));
      continue;
    }

    /* Signal that initialization is complete */
    g_ivshmem_init_done.test_and_set(std::memory_order_acquire);
    pthread_cond_signal(&g_ivshmem_init_cond);

    fprintf(stdout, "listen on server socket %d\n", g_ivshmem_client.sock_fd);

    if (ivshmem_cli_poll_events(&g_ivshmem_client) == 0) {
      continue;
    }

    /* disconnected from server, reset all peers */
    fprintf(stdout, "disconnected from server\n");

    ivshmem_client_close(&g_ivshmem_client);
  }
  return NULL;
}

int32_t ivshmem_cli_init(void) {
  printf("ivshmem_cli_init\n");
  struct sigaction sa;
  /* Ignore SIGPIPE, see this link for more info:
   * http://www.mail-archive.com/<EMAIL>/msg01606.html */
  sa.sa_handler = SIG_IGN;
  sa.sa_flags = 0;
  if (sigemptyset(&sa.sa_mask) == -1 || sigaction(SIGPIPE, &sa, 0) == -1) {
    perror("failed to ignore SIGPIPE; sigaction");
    return 1;
  }

#if defined(BUILD_C7X_1)
  int64_t peer_id = core2peer_converter(IPC_C7X_1);
#elif defined(BUILD_C7X_2)
  int64_t peer_id = core2peer_converter(IPC_C7X_2);
#else
  int64_t peer_id = core2peer_converter(IPC_MPU1_0);
#endif

  if (ivshmem_client_init(&g_ivshmem_client, IVSHMEM_DEFAULT_UDS_PATH,
                          ivshmem_client_notification_cb, NULL,
                          IVSHMEM_CLIENT_DEFAULT_VERBOSE, peer_id) < 0) {
    fprintf(stderr, "cannot init client\n");
    return 1;
  }

  /* Enable ivshmem client running flag*/
  g_ivshmem_running.test_and_set(std::memory_order_acquire);

  /* Start a thread to handle ivshmem events */
  if (pthread_create(&g_ivshmem_thread, NULL, ivshmem_cli_thr_fn, NULL) != 0) {
    printf("Failed to create ivshmem event handler thread\n");
    ivshmem_client_close(&g_ivshmem_client);
    return 1;
  }

  /* Wait for thread to initialize (with timeout) */
  static struct timespec ts;
  clock_gettime(CLOCK_REALTIME, &ts);
  ts.tv_sec += 5; /* 5 second timeout */

  pthread_mutex_lock(&g_ivshmem_mutex);
  int wait_result =
      pthread_cond_timedwait(&g_ivshmem_init_cond, &g_ivshmem_mutex, &ts);
  pthread_mutex_unlock(&g_ivshmem_mutex);

  if (wait_result != 0) {
    g_ivshmem_running.clear(std::memory_order_release);
    pthread_join(g_ivshmem_thread, NULL);
    fprintf(stderr, "Shutdown ivshmem client thread due to error(%d).\n",
            wait_result);
    return 1;
  }

  pthread_detach(g_ivshmem_thread);
  return 0;
}

int32_t ivshmem_cli_uninit(void) {
  int32_t status = 0;

  return status;
}

void ivshmem_cli_kick_all(void) {
  ivshmem_client_notify_broadcast(&g_ivshmem_client);
}
