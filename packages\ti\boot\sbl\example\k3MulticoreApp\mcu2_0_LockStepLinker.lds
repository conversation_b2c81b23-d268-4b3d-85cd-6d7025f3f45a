/*----------------------------------------------------------------------------*/
/* File: k3m4_r5f_linker.cmd                                                  */
/* Description:																  */
/*    Link command file for AM65XX M4 MCU 0 view							  */
/*	  TI ARM Compiler version 15.12.3 LTS or later							  */
/*                                                                            */
/*    Platform: QT                                                            */
/* (c) Texas Instruments 2017, All rights reserved.                           */
/*----------------------------------------------------------------------------*/
/*  History:															      *'
/*    Aug 26th, 2016 Original version .......................... <PERSON><PERSON>ong   */
/*    Aug 01th, 2017 new TCM mem map  .......................... <PERSON><PERSON>   */
/*    Nov 07th, 2017 Changes for R5F Init Code.................. Vivek Dhande */
/*    Mar 26th, 2019 Changes for R5F startup Code............... Vivek Dhande */
/*----------------------------------------------------------------------------*/
/* Linker Settings                                                            */
/* Standard linker options													  */
--retain="*(.bootCode)"
--retain="*(.startupCode)"
--retain="*(.startupData)"
--retain="*(.intvecs)"
--retain="*(.intc_text)"
--retain="*(.rstvectors)"
--retain="*(.irqStack)"
--retain="*(.fiqStack)"
--retain="*(.abortStack)"
--retain="*(.undStack)"
--retain="*(.svcStack)"
--fill_value=0
--stack_size=0x2000
--heap_size=0x1000
--entry_point=_resetvectors		/* Default C RTS boot.asm	*/

-stack  0x2000                              /* SOFTWARE STACK SIZE           */
-heap   0x2000                              /* HEAP AREA SIZE                */

/* Stack Sizes for various modes */
__IRQ_STACK_SIZE = 0x1000;
__FIQ_STACK_SIZE = 0x1000;
__ABORT_STACK_SIZE = 0x1000;
__UND_STACK_SIZE = 0x1000;
__SVC_STACK_SIZE = 0x1000;

/*----------------------------------------------------------------------------*/
/* Memory Map                                                                 */
MEMORY
{
	VECTORS (X)  			: origin=0x7006F000 length=0x1000
    /*  Reset Vectors base address(RESET_VECTORS) should be 64 bytes aligned  */
	RESET_VECTORS (X)  			: origin=0x70050000 length=0x100
	MSMC3_MCU1_CPU0	: origin=0x70050100 length=0x20000 - 0x1100	/* 128KB */

/* Additional memory settings	*/

}  /* end of MEMORY */

/*----------------------------------------------------------------------------*/
/* Section Configuration                                                      */

SECTIONS
{
/* 'intvecs' and 'intc_text' sections shall be placed within                  */
/* a range of +\- 16 MB                                                       */
    .intvecs 	: {} palign(8) 		> VECTORS
    .intc_text 	: {} palign(8) 		> VECTORS
    .rstvectors 	: {} palign(8) 		> RESET_VECTORS
    .bootCode    	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .startupCode 	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .startupData 	: {} palign(8) 		> MSMC3_MCU1_CPU0, type = NOINIT
    .text    	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .const   	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .rodata   	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .cinit   	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .pinit   	: {} palign(8) 		> MSMC3_MCU1_CPU0
    .bss     	: {} align(4)  		> MSMC3_MCU1_CPU0
    .bss.devgroup     : {*(.bss.devgroup.*)} align(4)      > MSMC3_MCU1_CPU0
    .const.devgroup   : {*(.const.devgroup.*)} align(4)      > MSMC3_MCU1_CPU0
    .data    	: {} palign(128) 	> MSMC3_MCU1_CPU0
    .boardcfg_data        : {} palign(128)           > MSMC3_MCU1_CPU0
	.sysmem  	: {} 				> MSMC3_MCU1_CPU0
    .bss:extMemCache:ramdisk : {} align (32)     > MSMC3_MCU1_CPU0
	.stack  	: {} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
	.irqStack  	: {. = . + __IRQ_STACK_SIZE;} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
    RUN_START(__IRQ_STACK_START)
    RUN_END(__IRQ_STACK_END)
    .fiqStack  	: {. = . + __FIQ_STACK_SIZE;} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
    RUN_START(__FIQ_STACK_START)
    RUN_END(__FIQ_STACK_END)
    .abortStack  	: {. = . + __ABORT_STACK_SIZE;} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
    RUN_START(__ABORT_STACK_START)
    RUN_END(__ABORT_STACK_END)
    .undStack  	: {. = . + __UND_STACK_SIZE;} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
    RUN_START(__UND_STACK_START)
    RUN_END(__UND_STACK_END)
    .svcStack  	: {. = . + __SVC_STACK_SIZE;} align(4)		> MSMC3_MCU1_CPU0  (HIGH)
    RUN_START(__SVC_STACK_START)
    RUN_END(__SVC_STACK_END)

/* Additional sections settings 	*/

}  /* end of SECTIONS */

/*----------------------------------------------------------------------------*/
/* Misc linker settings                                                       */


/*-------------------------------- END ---------------------------------------*/
