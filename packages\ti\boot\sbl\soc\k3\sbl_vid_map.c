/*
 * Copyright (C) 2019 Texas Instruments Incorporated - http://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the
 * distribution.
 *
 * Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include <ti/csl/soc.h>
uint16_t sblMapOtpVidToMilliVolts[256] =
{
    000,
    320,
    340,
    360,
    380,
    400,
    420,
    440,
    460,
    480,
    500,
    520,
    540,
    560,
    580,
    600,
    605,
    610,
    615,
    620,
    625,
    630,
    635,
    640,
    645,
    650,
    655,
    660,
    665,
    670,
    675,
    680,
    685,
    690,
    695,
    700,
    705,
    710,
    715,
    720,
    725,
    730,
    735,
    740,
    745,
    750,
    755,
    760,
    765,
    770,
    775,
    780,
    785,
    790,
    795,
    800,
    805,
    810,
    815,
    820,
    825,
    830,
    835,
    840,
    845,
    850,
    855,
    860,
    865,
    870,
    875,
    880,
    885,
    890,
    895,
    900,
    905,
    910,
    915,
    920,
    925,
    930,
    935,
    940,
    945,
    950,
    955,
    960,
    965,
    970,
    975,
    980,
    985,
    990,
    995,
    1000,
    1005,
    1010,
    1015,
    1020,
    1025,
    1030,
    1035,
    1040,
    1045,
    1050,
    1055,
    1060,
    1065,
    1070,
    1075,
    1080,
    1085,
    1090,
    1095,
    1100,
    1110,
    1120,
    1130,
    1140,
    1150,
    1160,
    1170,
    1180,
    1190,
    1200,
    1210,
    1220,
    1230,
    1240,
    1250,
    1260,
    1270,
    1280,
    1290,
    1300,
    1310,
    1320,
    1330,
    1340,
    1350,
    1360,
    1370,
    1380,
    1390,
    1400,
    1410,
    1420,
    1430,
    1440,
    1450,
    1460,
    1470,
    1480,
    1490,
    1500,
    1510,
    1520,
    1530,
    1540,
    1550,
    1560,
    1570,
    1580,
    1590,
    1600,
    1610,
    1620,
    1630,
    1640,
    1650,
    1660,
    1680,
    1700,
    1720,
    1740,
    1760,
    1780,
    1800,
    1820,
    1840,
    1860,
    1880,
    1900,
    1920,
    1940,
    1960,
    1980,
    2000,
    2020,
    2040,
    2060,
    2080,
    2100,
    2120,
    2140,
    2160,
    2180,
    2200,
    2220,
    2240,
    2260,
    2280,
    2300,
    2320,
    2340,
    2360,
    2380,
    2400,
    2420,
    2440,
    2460,
    2480,
    2500,
    2520,
    2540,
    2560,
    2580,
    2600,
    2620,
    2640,
    2660,
    2680,
    2700,
    2720,
    2740,
    2760,
    2780,
    2800,
    2820,
    2840,
    2860,
    2880,
    2900,
    2920,
    2940,
    2960,
    2980,
    3000,
    3020,
    3040,
    3060,
    3080,
    3100,
    3120,
    3140,
    3160,
    3180,
    3200,
    3220,
    3240,
    3260,
    3280,
    3300,
    3320,
    3340
};
