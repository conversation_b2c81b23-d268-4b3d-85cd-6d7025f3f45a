/*
 * Copyright 6WIND S.A., 2014
 *
 * This work is licensed under the terms of the GNU GPL, version 2 or
 * (at your option) any later version.  See the COPYING file in the
 * top-level directory.
 */

#include <cerrno>
#include <sys/socket.h>
#include <sys/un.h>

#include "qemu/queue.h"

#include "ivshmem-client.h"

#include <ti/drv/ipc/src/mailbox/V0/cslr_mailbox.h>

/* log a message on stdout if verbose=1 */
#define IVSHMEM_CLIENT_DEBUG(client, fmt, ...)                                 \
  do {                                                                         \
    printf(fmt, ##__VA_ARGS__);                                                \
  } while (0)

/* Mailbox Cluster Base Address */
static uintptr_t g_IPC_Mailbox_BasePhyAddr[IPC_MAILBOX_CLUSTER_CNT] =
{
    CSL_NAVSS_MAIN_MAILBOX_REGS_0_BASE,     /* Mailbox - cluster0   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_1_BASE,     /* Mailbox - cluster1   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_2_BASE,     /* Mailbox - cluster2   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_3_BASE,     /* Mailbox - cluster3   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_4_BASE,     /* Mailbox - cluster4   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_5_BASE,     /* Mailbox - cluster5   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_6_BASE,     /* Mailbox - cluster6   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_7_BASE,     /* Mailbox - cluster7   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_8_BASE,     /* Mailbox - cluster8   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_9_BASE,     /* Mailbox - cluster9   */
    CSL_NAVSS_MAIN_MAILBOX_REGS_10_BASE,    /* Mailbox - cluster10  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_11_BASE,    /* Mailbox - cluster11  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_12_BASE,    /* Mailbox - cluster12  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_13_BASE,    /* Mailbox - cluster13  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_14_BASE,    /* Mailbox - cluster14  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_15_BASE,    /* Mailbox - cluster15  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_16_BASE,    /* Mailbox - cluster16  */
    CSL_NAVSS_MAIN_MAILBOX_REGS_17_BASE,    /* Mailbox - cluster17  */
};

static Ipc_MailboxInfo   g_IPC_MailboxInfo[IPC_MAX_PROCS][IPC_MAX_PROCS] =
{
    /* Host Processor - A72-vm0	*/
    {
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,    0U} },  /* Self - A72-vm0 */
        { {    0U,    0U,  0U}, {    0U,    0U,    1U} },  /* mcu-r5f0 */
        { {    0U,    0U,  2U}, {    0U,    0U,    3U} },  /* mcu-r5f1 */
        { {    1U,    0U,  0U}, {    1U,    0U,    1U} },  /* main-r5f0 */
        { {    1U,    0U,  2U}, {    1U,    0U,    3U} },  /* main-r5f1 */
        { {    2U,    0U,  0U}, {    2U,    0U,    1U} },  /* main-r5f2 */
        { {    2U,    0U,  2U}, {    2U,    0U,    3U} },  /* main-r5f3 */
        { {    3U,    0U,  0U}, {    3U,    0U,    1U} },  /* main-r5f4 */
        { {    3U,    0U,  2U}, {    3U,    0U,    3U} },  /* main-r5f5 */
        { {    4U,    0U,  0U}, {    4U,    0U,    1U} },  /* C7x-1 */
        { {    4U,    0U,  2U}, {    4U,    0U,    3U} },  /* C7x-2 */
        { {    5U,    0U,  0U}, {    5U,    0U,    1U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    5U,    0U,  2U}, {    5U,    0U,    3U} },  /* C7x-4 */
#endif
        { {    0U,    0U, 10U}, {    0U,    0U,   11U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu1_0 	*/
    {
        { {    0U,    1U,  1U }, {    0U,    1U,  0U} },  /* A72-vm0 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - mcu-r5f0 */
        { {    0U,    1U,  4U }, {    0U,    1U,  5U} },  /* mcu-r5f1 */
        { {   12U,    0U,  0U }, {    6U, 0xFFU,  4U} },  /* main-r5f0 */
        { {   12U,    0U,  1U }, {    7U, 0xFFU,  4U} },  /* main-r5f1 */
        { {   12U,    0U,  2U }, {    8U, 0xFFU,  4U} },  /* main-r5f2 */
        { {   12U,    0U,  3U }, {    9U, 0xFFU,  4U} },  /* main-r5f3 */
        { {   12U,    0U,  4U }, {   10U, 0xFFU,  4U} },  /* main-r5f4 */
        { {   12U,    1U,  5U }, {   11U, 0xFFU,  4U} },  /* main-r5f5 */
        { {   12U,    1U,  6U }, {   14U, 0xFFU,  6U} },  /* C7x-1 */
        { {   12U,    1U,  7U }, {   15U, 0xFFU,  6U} },  /* C7x-2 */
        { {   12U,    1U,  8U }, {   16U, 0xFFU,  6U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   12U,    1U,  9U }, {   17U, 0xFFU,  6U} },  /* C7x-4 */
#endif
        { {    0U,    1U,  7U }, {    0U,    1U,  6U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu1_1 */
    {
        { {    0U,    2U,  3U }, {    0U,    2U,  2U} },  /* A72-vm0 */
        { {    0U,    2U,  5U }, {    0U,    2U,  4U} },  /* mcu-r5f0 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - mcu-r5f1 */
        { {   13U,    0U,  0U }, {    6U, 0xFFU,  5U} },  /* main-r5f0 */
        { {   13U,    0U,  1U }, {    7U, 0xFFU,  5U} },  /* main-r5f1 */
        { {   13U,    0U,  2U }, {    8U, 0xFFU,  5U} },  /* main-r5f2 */
        { {   13U,    0U,  3U }, {    9U, 0xFFU,  5U} },  /* main-r5f3 */
        { {   13U,    0U,  4U }, {   10U, 0xFFU,  5U} },  /* main-r5f4 */
        { {   13U,    1U,  5U }, {   11U, 0xFFU,  5U} },  /* main-r5f5 */
        { {   13U,    1U,  6U }, {   14U, 0xFFU,  7U} },  /* C7x-1 */
        { {   13U,    1U,  7U }, {   15U, 0xFFU,  7U} },  /* C7x-2 */
        { {   13U,    1U,  8U }, {   16U, 0xFFU,  7U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   13U,    1U,  9U }, {   17U, 0xFFU,  7U} },  /* C7x-4 */
#endif
        { {    0U,    2U,  9U }, {    0U,    2U,  8U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu2_0  */
    {
        { {    1U,    1U,  1U}, {    1U,    1U, 0U} },  /* A72-vm0 */
        { {    6U,    0U,  4U}, {   12U, 0xFFU, 0U} },  /* mcu-r5f0 */
        { {    6U,    1U,  5U}, {   13U, 0xFFU, 0U} },  /* mcu-r5f1 */
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU, 0U} },  /* Self - main-r5f0 */
        { {    1U,    1U,  4U}, {    1U,    1U, 5U} },  /* main-r5f1 */
        { {    6U,    0U,  0U}, {    8U, 0xFFU, 0U} },  /* main-r5f2 */
        { {    6U,    0U,  1U}, {    9U, 0xFFU, 0U} },  /* main-r5f3 */
        { {    6U,    0U,  2U}, {   10U, 0xFFU, 0U} },  /* main-r5f4 */
        { {    6U,    0U,  3U}, {   11U, 0xFFU, 0U} },  /* main-r5f5 */
        { {    6U,    1U,  6U}, {   14U, 0xFFU, 0U} },  /* C7x-1 */
        { {    6U,    1U,  7U}, {   15U, 0xFFU, 0U} },  /* C7x-2 */
        { {    6U,    1U,  8U}, {   16U, 0xFFU, 0U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    6U,    1U,  9U}, {   17U, 0xFFU, 0U} },  /* C7x-4 */
#endif
        { {    1U,    1U,  7U}, {    1U,    1U, 6U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu2_1 */
    {
        { {    1U,    2U,  3U }, {    1U,    2U, 2U} },  /* A72-vm0 */
        { {    7U,    0U,  4U }, {   12U, 0xFFU, 1U} },  /* mcu-r5f0 */
        { {    7U,    1U,  5U }, {   13U, 0xFFU, 1U} },  /* mcu-r5f1 */
        { {    1U,    2U,  5U }, {    1U,    2U, 4U} },  /* main-r5f0 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU, 0U} },  /* Self - main-r5f1 */
        { {    7U,    0U,  0U }, {    8U, 0xFFU, 1U} },  /* main-r5f2 */
        { {    7U,    0U,  1U }, {    9U, 0xFFU, 1U} },  /* main-r5f3 */
        { {    7U,    0U,  2U }, {   10U, 0xFFU, 1U} },  /* main-r5f4 */
        { {    7U,    0U,  3U }, {   11U, 0xFFU, 1U} },  /* main-r5f5 */
        { {    7U,    1U,  6U }, {   14U, 0xFFU, 1U} },  /* C7x-1 */
        { {    7U,    1U,  7U }, {   15U, 0xFFU, 1U} },  /* C7x-2 */
        { {    7U,    1U,  8U }, {   16U, 0xFFU, 1U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    7U,    1U,  9U }, {   17U, 0xFFU, 1U} },  /* C7x-4 */
#endif
        { {    1U,    2U,  9U }, {    1U,    2U, 8U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu3_0 */
    {
        { {    2U,    1U,  1U }, {    2U,    1U,  0U} },  /* A72-vm0 */
        { {    8U,    0U,  4U }, {   12U, 0xFFU,  2U} },  /* mcu-r5f0 */
        { {    8U,    1U,  5U }, {   13U, 0xFFU,  2U} },  /* mcu-r5f1 */
        { {    8U,    0U,  0U }, {    6U, 0xFFU,  0U} },  /* main-r5f0 */
        { {    8U,    0U,  1U }, {    7U, 0xFFU,  0U} },  /* main-r5f1 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - main-r5f2 */
        { {    2U,    1U,  4U }, {    2U,    1U,  5U} },  /* main-r5f3 */
        { {    8U,    0U,  2U }, {   10U, 0xFFU,  2U} },  /* main-r5f4 */
        { {    8U,    0U,  3U }, {   11U, 0xFFU,  2U} },  /* main-r5f5 */
        { {    8U,    1U,  6U }, {   14U, 0xFFU,  2U} },  /* C7x-1 */
        { {    8U,    1U,  7U }, {   15U, 0xFFU,  2U} },  /* C7x-2 */
        { {    8U,    1U,  8U }, {   16U, 0xFFU,  2U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    8U,    1U,  9U }, {   17U, 0xFFU,  2U} },  /* C7x-4 */
#endif
        { {    2U,    1U,  7U }, {    2U,    1U,  6U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu3_1	*/
    {
        { {    2U,    2U,  3U }, {    2U,    2U,  2U} },  /* A72-vm0 */
        { {    9U,    0U,  4U }, {   12U, 0xFFU,  3U} },  /* mcu-r5f0 */
        { {    9U,    1U,  5U }, {   13U, 0xFFU,  3U} },  /* mcu-r5f1 */
        { {    9U,    0U,  0U }, {    6U, 0xFFU,  1U} },  /* main-r5f0 */
        { {    9U,    0U,  1U }, {    7U, 0xFFU,  1U} },  /* main-r5f1 */
        { {    2U,    2U,  5U }, {    2U,    2U,  4U} },  /* main-r5f2 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - main-r5f3 */
        { {    9U,    0U,  2U }, {   10U, 0xFFU,  3U} },  /* main-r5f4 */
        { {    9U,    0U,  3U }, {   11U, 0xFFU,  3U} },  /* main-r5f5 */
        { {    9U,    1U,  6U }, {   14U, 0xFFU,  3U} },  /* C7x-1 */
        { {    9U,    1U,  7U }, {   15U, 0xFFU,  3U} },  /* C7x-2 */
        { {    9U,    1U,  8U }, {   16U, 0xFFU,  3U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    9U,    1U,  9U }, {   17U, 0xFFU,  3U} },  /* C7x-4 */
#endif
        { {    2U,    2U,  9U }, {    2U,    2U,  8U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu4_0 */
    {
        { {    3U,    1U,  1U }, {    3U,    1U,  0U} },  /* A72-vm0 */
        { {   10U,    0U,  4U }, {   12U, 0xFFU,  4U} },  /* mcu-r5f0 */
        { {   10U,    1U,  5U }, {   13U, 0xFFU,  4U} },  /* mcu-r5f1 */
        { {   10U,    0U,  0U }, {    6U, 0xFFU,  2U} },  /* main-r5f0 */
        { {   10U,    0U,  1U }, {    7U, 0xFFU,  2U} },  /* main-r5f1 */
        { {   10U,    0U,  2U }, {    8U, 0xFFU,  2U} },  /* main-r5f2 */
        { {   10U,    0U,  3U }, {    9U, 0xFFU,  2U} },  /* main-r5f3 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - main-r5f4 */
        { {    3U,    1U,  4U }, {    3U,    1U,  5U} },  /* main-r5f5 */
        { {   10U,    1U,  6U }, {   14U, 0xFFU,  4U} },  /* C7x-1 */
        { {   10U,    1U,  7U }, {   15U, 0xFFU,  4U} },  /* C7x-2 */
        { {   10U,    1U,  8U }, {   16U, 0xFFU,  4U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   10U,    1U,  9U }, {   17U, 0xFFU,  4U} },  /* C7x-4 */
#endif
        { {    3U,    1U,  7U }, {    3U,    1U,  6U} }   /* A72-vm1 */
    },
    /* Host Processor - mcu4_1	*/
    {
        { {    3U,    2U,  3U }, {    3U,    2U,  2U} },  /* A72-vm0 */
        { {   11U,    0U,  4U }, {   12U, 0xFFU,  5U} },  /* mcu-r5f0 */
        { {   11U,    1U,  5U }, {   13U, 0xFFU,  5U} },  /* mcu-r5f1 */
        { {   11U,    0U,  0U }, {    6U, 0xFFU,  3U} },  /* main-r5f0 */
        { {   11U,    0U,  1U }, {    7U, 0xFFU,  3U} },  /* main-r5f1 */
        { {   11U,    0U,  2U }, {    8U, 0xFFU,  3U} },  /* main-r5f2 */
        { {   11U,    0U,  3U }, {    9U, 0xFFU,  3U} },  /* main-r5f3 */
        { {    3U,    2U,  5U }, {    3U,    2U,  4U} },  /* main-r5f4 */
        { { 0xFFU, 0xFFU,  0U }, { 0xFFU, 0xFFU,  0U} },  /* Self - main-r5f5 */
        { {   11U,    1U,  6U }, {   14U, 0xFFU,  5U} },  /* C7x-1 */
        { {   11U,    1U,  7U }, {   15U, 0xFFU,  5U} },  /* C7x-2 */
        { {   11U,    1U,  8U }, {   16U, 0xFFU,  5U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   11U,    1U,  9U }, {   17U, 0xFFU,  5U} },  /* C7x-4 */
#endif
        { {    3U,    2U,  9U }, {    3U,    2U,  8U} }   /* A72-vm1 */
    },
    /* Host Processor - c7x_1	*/
    {
        { {    4U,    1U,  1U}, {    4U,    1U,  0U} },  /* A72-vm0 */
        { {   14U,    1U,  6U}, {   12U, 0xFFU,  6U} },  /* mcu-r5f0 */
        { {   14U,    1U,  7U}, {   13U, 0xFFU,  6U} },  /* mcu-r5f1 */
        { {   14U,    0U,  0U}, {    6U, 0xFFU,  6U} },  /* main-r5f0 */
        { {   14U,    0U,  1U}, {    7U, 0xFFU,  6U} },  /* main-r5f1 */
        { {   14U,    0U,  2U}, {    8U, 0xFFU,  6U} },  /* main-r5f2 */
        { {   14U,    0U,  3U}, {    9U, 0xFFU,  6U} },  /* main-r5f3 */
        { {   14U,    0U,  4U}, {   10U, 0xFFU,  6U} },  /* main-r5f4 */
        { {   14U,    1U,  5U}, {   11U, 0xFFU,  6U} },  /* main-r5f5 */
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,  0U} },  /* Self - C7x-1 */
        { {    4U,    1U,  4U}, {    4U,    1U,  5U} },  /* C7x-2 */
        { {   14U,    1U,  8U}, {   16U, 0xFFU,  8U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   14U,    1U,  9U}, {   17U, 0xFFU,  8U} },  /* C7x-4 */
#endif
        { {    4U,    1U,  7U}, {    4U,    1U,  6U} }   /* A72-vm1 */
    },
    /* Host Processor - c7x_2	*/
    {
        { {    4U,    2U,  3U}, {    4U,    2U,  2U} },  /* A72-vm0 */
        { {   15U,    1U,  6U}, {   12U, 0xFFU,  7U} },  /* mcu-r5f0 */
        { {   15U,    1U,  7U}, {   13U, 0xFFU,  7U} },  /* mcu-r5f1 */
        { {   15U,    0U,  0U}, {    6U, 0xFFU,  7U} },  /* main-r5f0 */
        { {   15U,    0U,  1U}, {    7U, 0xFFU,  7U} },  /* main-r5f1 */
        { {   15U,    0U,  2U}, {    8U, 0xFFU,  7U} },  /* main-r5f2 */
        { {   15U,    0U,  3U}, {    9U, 0xFFU,  7U} },  /* main-r5f3 */
        { {   15U,    0U,  4U}, {   10U, 0xFFU,  7U} },  /* main-r5f4 */
        { {   15U,    1U,  5U}, {   11U, 0xFFU,  7U} },  /* main-r5f5 */
        { {    4U,    2U,  5U}, {    4U,    2U,  4U} },  /* C7x-1 */
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,  0U} },  /* Self - C7x-2 */
        { {   15U,    1U,  8U}, {   16U, 0xFFU,  9U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {   15U,    1U,  9U}, {   17U, 0xFFU,  9U} },  /* C7x-4 */
#endif
        { {    4U,    2U,  9U}, {    4U,    2U,  8U} }   /* A72-vm1 */
    },
    /* Host Processor - c7x_3	*/
    {
        { {    5U,    1U,  1U}, {    5U,    1U,  0U} },  /* A72-vm0 */
        { {   16U,    1U,  6U}, {   12U, 0xFFU,  8U} },  /* mcu-r5f0 */
        { {   16U,    1U,  7U}, {   13U, 0xFFU,  8U} },  /* mcu-r5f1 */
        { {   16U,    0U,  0U}, {    6U, 0xFFU,  8U} },  /* main-r5f0 */
        { {   16U,    0U,  1U}, {    7U, 0xFFU,  8U} },  /* main-r5f1 */
        { {   16U,    0U,  2U}, {    8U, 0xFFU,  8U} },  /* main-r5f2 */
        { {   16U,    0U,  3U}, {    9U, 0xFFU,  8U} },  /* main-r5f3 */
        { {   16U,    0U,  4U}, {   10U, 0xFFU,  8U} },  /* main-r5f4 */
        { {   16U,    1U,  5U}, {   11U, 0xFFU,  8U} },  /* main-r5f5 */
        { {   16U,    1U,  8U}, {   14U, 0xFFU,  8U} },  /* C7x-1 */
        { {   16U,    1U,  9U}, {   15U, 0xFFU,  8U} },  /* C7x-2 */
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,  0U} },  /* Self - C7x-3 */
#if defined (SOC_J784S4)
        { {    5U,    1U,  4U}, {    5U,    1U,  5U} },  /* C7x-4 */
#endif
        { {    5U,    1U,  7U}, {    5U,    1U,  6U} }   /* A72-vm1 */
    },
#if defined (SOC_J784S4)
    /* Host Processor - c7x_4	*/
    {
        { {    5U,    2U,  3U}, {    5U,    2U,  2U} },  /* A72-vm0 */
        { {   17U,    1U,  6U}, {   12U, 0xFFU,  9U} },  /* mcu-r5f0 */
        { {   17U,    1U,  7U}, {   13U, 0xFFU,  9U} },  /* mcu-r5f1 */
        { {   17U,    0U,  0U}, {    6U, 0xFFU,  9U} },  /* main-r5f0 */
        { {   17U,    0U,  1U}, {    7U, 0xFFU,  9U} },  /* main-r5f1 */
        { {   17U,    0U,  2U}, {    8U, 0xFFU,  9U} },  /* main-r5f2 */
        { {   17U,    0U,  3U}, {    9U, 0xFFU,  9U} },  /* main-r5f3 */
        { {   17U,    0U,  4U}, {   10U, 0xFFU,  9U} },  /* main-r5f4 */
        { {   17U,    1U,  5U}, {   11U, 0xFFU,  9U} },  /* main-r5f5 */
        { {   17U,    1U,  8U}, {   14U, 0xFFU,  9U} },  /* C7x-1 */
        { {   17U,    1U,  9U}, {   15U, 0xFFU,  9U} },  /* C7x-2 */
        { {    5U,    2U,  5U}, {    5U,    2U,  4U} },  /* C7x-3 */
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,  0U} },  /* Self - C7x-4 */
        { {    5U,    2U,  9U}, {    5U,    2U,  8U} }   /* A72-vm1 */
    },
#endif
    /* Host Processor - A72-vm1	*/
    {
        { {    0U,    3U, 11U}, {    0U,    3U, 10U} },  /* A72-vm0 */
        { {    0U,    3U,  6U}, {    0U,    3U,  7U} },  /* mcu-r5f0 */
        { {    0U,    3U,  8U}, {    0U,    3U,  9U} },  /* mcu-r5f1 */
        { {    1U,    3U,  6U}, {    1U,    3U,  7U} },  /* main-r5f0 */
        { {    1U,    3U,  8U}, {    1U,    3U,  9U} },  /* main-r5f1 */
        { {    2U,    3U,  6U}, {    2U,    3U,  7U} },  /* main-r5f2 */
        { {    2U,    3U,  8U}, {    2U,    3U,  9U} },  /* main-r5f3 */
        { {    3U,    3U,  6U}, {    3U,    3U,  7U} },  /* main-r5f4 */
        { {    3U,    3U,  8U}, {    3U,    3U,  9U} },  /* main-r5f5 */
        { {    4U,    3U,  6U}, {    4U,    3U,  7U} },  /* C7x-1 */
        { {    4U,    3U,  8U}, {    4U,    3U,  9U} },  /* C7x-2 */
        { {    5U,    3U,  6U}, {    5U,    3U,  7U} },  /* C7x-3 */
#if defined (SOC_J784S4)
        { {    5U,    3U,  8U}, {    5U,    3U,  9U} },  /* C7x-4 */
#endif
        { { 0xFFU, 0xFFU,  0U}, { 0xFFU, 0xFFU,  0U} }   /* Self - A72-vm1 */
    }
};

/* read message from the unix socket */
static int ivshmem_client_read_one_msg(IvshmemClient *client, uint64_t *index,
                                       int *fd) {
  int ret;
  struct msghdr msg;
  struct iovec iov[1];
  union {
    struct cmsghdr cmsg;
    char control[CMSG_SPACE(sizeof(int))];
  } msg_control;
  struct cmsghdr *cmsg;

  iov[0].iov_base = index;
  iov[0].iov_len = sizeof(*index);

  memset(&msg, 0, sizeof(msg));
  msg.msg_iov = iov;
  msg.msg_iovlen = 1;
  msg.msg_control = &msg_control;
  msg.msg_controllen = sizeof(msg_control);

  ret = recvmsg(client->sock_fd, &msg, 0);
  if (ret < sizeof(*index)) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot read message: %s\n", strerror(errno));
    return -1;
  }
  if (ret == 0) {
    IVSHMEM_CLIENT_DEBUG(client, "lost connection to server\n");
    return -1;
  }

  *index = GUINT64_FROM_LE(*index);
  *fd = -1;

  for (cmsg = CMSG_FIRSTHDR(&msg); cmsg; cmsg = CMSG_NXTHDR(&msg, cmsg)) {

    if (cmsg->cmsg_len != CMSG_LEN(sizeof(int)) ||
        cmsg->cmsg_level != SOL_SOCKET || cmsg->cmsg_type != SCM_RIGHTS) {
      continue;
    }

    memcpy(fd, CMSG_DATA(cmsg), sizeof(*fd));
  }

  return 0;
}

/* send message to the unix socket */
static int ivshmem_client_send_one_msg(IvshmemClient *client, int64_t value) {
  int ret;
  struct msghdr msg;
  struct iovec iov[1];

  value = GINT64_TO_LE(value);
  iov[0].iov_base = &value;
  iov[0].iov_len = sizeof(value);

  memset(&msg, 0, sizeof(msg));
  msg.msg_iov = iov;
  msg.msg_iovlen = 1;

  ret = sendmsg(client->sock_fd, &msg, 0);
  if (ret <= 0) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot send message: %s\n", strerror(errno));
    return -1;
  }

  return 0;
}

/* free a peer when the server advertises a disconnection or when the
 * client is freed */
static void ivshmem_client_free_peer(IvshmemClient *client,
                                     IvshmemClientPeer *peer) {
  unsigned vector;

  QTAILQ_REMOVE(&client->peer_list, peer, next);
  for (vector = 0; vector < peer->vectors_count; vector++) {
    close(peer->vectors[vector]);
  }

  g_free(peer);
}

/* handle message coming from server (new peer, new vectors) */
static int ivshmem_client_handle_server_msg(IvshmemClient *client) {
  IvshmemClientPeer *peer;
  uint64_t peer_id;
  int ret, fd;

  ret = ivshmem_client_read_one_msg(client, &peer_id, &fd);
  if (ret < 0) {
    return -1;
  }

  /* can return a peer or the local client */
  peer = ivshmem_client_search_peer(client, peer_id);

  /* delete peer */
  if (fd == -1) {

    if (peer == NULL || peer == &client->local) {
      IVSHMEM_CLIENT_DEBUG(client,
                           "receive delete for invalid "
                           "peer %" PRId64 "\n",
                           peer_id);
      return -1;
    }

    IVSHMEM_CLIENT_DEBUG(client, "delete peer id = %" PRId64 "\n", peer_id);
    ivshmem_client_free_peer(client, peer);
    return 0;
  }

  /* new peer */
  if (peer == NULL) {
    peer = (IvshmemClientPeer *)g_malloc0(sizeof(*peer));
    peer->id = peer_id;
    peer->vectors_count = 0;
    QTAILQ_INSERT_TAIL(&client->peer_list, peer, next);
    IVSHMEM_CLIENT_DEBUG(client, "new peer id = %" PRId64 "\n", peer_id);
  }

  /* new vector */
  IVSHMEM_CLIENT_DEBUG(client,
                       "new vector %d (fd=%d) for peer id %" PRId64 "\n",
                       peer->vectors_count, fd, peer->id);
  if (peer->vectors_count >= G_N_ELEMENTS(peer->vectors)) {
    IVSHMEM_CLIENT_DEBUG(client, "Too many vectors received, failing");
    return -1;
  }

  peer->vectors[peer->vectors_count] = fd;
  peer->vectors_count++;

  return 0;
}

/* init a new ivshmem client */
int ivshmem_client_init(IvshmemClient *client, const char *unix_sock_path,
                        IvshmemClientNotifCb notif_cb, void *notif_arg,
                        bool verbose, uint64_t peer_id) {
  int ret;
  unsigned i;

  memset(client, 0, sizeof(*client));

  ret = snprintf(client->unix_sock_path, sizeof(client->unix_sock_path), "%s",
                 unix_sock_path);

  if (ret < 0 || ret >= sizeof(client->unix_sock_path)) {
    IVSHMEM_CLIENT_DEBUG(client, "could not copy unix socket path\n");
    return -1;
  }

  for (i = 0; i < IVSHMEM_MAX_VECTORS; i++) {
    client->local.vectors[i] = -1;
  }

  QTAILQ_INIT(&client->peer_list);
  client->local.id = peer_id;

  client->notif_cb = notif_cb;
  client->notif_arg = notif_arg;
  client->verbose = verbose;
  client->vring_shm_fd = -1;
  client->mailbox_shm_fd = -1;
  client->sock_fd = -1;

  return 0;
}

/* create and connect to the unix socket */
int ivshmem_client_connect(IvshmemClient *client) {
  struct sockaddr_un s_un;
  int fd, ret;
  uint64_t tmp;

  IVSHMEM_CLIENT_DEBUG(client, "connect to server %s\n",
                       client->unix_sock_path);

  client->sock_fd = socket(AF_UNIX, SOCK_STREAM, 0);
  if (client->sock_fd < 0) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot create socket: %s\n", strerror(errno));
    return -1;
  }

  s_un.sun_family = AF_UNIX;
  ret = snprintf(s_un.sun_path, sizeof(s_un.sun_path), "%s",
                 client->unix_sock_path);
  if (ret < 0 || ret >= sizeof(s_un.sun_path)) {
    IVSHMEM_CLIENT_DEBUG(client, "could not copy unix socket path\n");
    goto err_close;
  }

  if (connect(client->sock_fd, (struct sockaddr *)&s_un, sizeof(s_un)) < 0) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot connect to %s: %s\n", s_un.sun_path,
                         strerror(errno));
    goto err_close;
  }

  /* first, send our core ID to the server */
  IVSHMEM_CLIENT_DEBUG(client, "sending peer_id %" PRId64 " to server\n",
                       client->local.id);
  if (ivshmem_client_send_one_msg(client, client->local.id) < 0) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot send peer_id to server\n");
    goto err_close;
  }

  /* then, we expect a protocol version */
  if (ivshmem_client_read_one_msg(client, &tmp, &fd) < 0 ||
      (tmp != IVSHMEM_PROTOCOL_VERSION) || fd != -1) {
    IVSHMEM_CLIENT_DEBUG(client, "cannot read from server\n");
    goto err_close;
  }

  /* now, we expect shared mem fd + a -1 index, note that shm fd
   * is not used */
  if (ivshmem_client_read_one_msg(client, &tmp, &fd) < 0 || tmp != -1 ||
      fd < 0) {
    if (fd >= 0) {
      close(fd);
    }
    IVSHMEM_CLIENT_DEBUG(client, "cannot read from server (3)\n");
    goto err_close;
  }
  client->vring_shm_fd = fd;
  IVSHMEM_CLIENT_DEBUG(client, "vring_shm_fd=%d\n", fd);

  /* now, we expect shared mem fd + a -1 index, note that shm fd
   * is not used */
  if (ivshmem_client_read_one_msg(client, &tmp, &fd) < 0 || tmp != -1 ||
      fd < 0) {
    if (fd >= 0) {
      close(fd);
    }
    IVSHMEM_CLIENT_DEBUG(client, "cannot read from server (3)\n");
    goto err_close;
  }
  client->mailbox_shm_fd = fd;
  IVSHMEM_CLIENT_DEBUG(client, "mailbox_shm_fd=%d\n", fd);

  return 0;

err_close:
  close(client->sock_fd);
  client->sock_fd = -1;
  return -1;
}

/* close connection to the server, and free all peer structures */
void ivshmem_client_close(IvshmemClient *client) {
  IvshmemClientPeer *peer;
  unsigned i;

  IVSHMEM_CLIENT_DEBUG(client, "close client\n");

  while ((peer = QTAILQ_FIRST(&client->peer_list)) != NULL) {
    ivshmem_client_free_peer(client, peer);
  }

  close(client->vring_shm_fd);
  client->vring_shm_fd = -1;
  close(client->mailbox_shm_fd);
  client->mailbox_shm_fd = -1;
  close(client->sock_fd);
  client->sock_fd = -1;
  client->local.id = -1;
  for (i = 0; i < IVSHMEM_MAX_VECTORS; i++) {
    close(client->local.vectors[i]);
    client->local.vectors[i] = -1;
  }
  client->local.vectors_count = 0;
}

/* get the fd_set according to the unix socket and peer list */
void ivshmem_client_get_fds(const IvshmemClient *client, fd_set *fds,
                            int *maxfd) {
  int fd;
  unsigned vector;

  FD_SET(client->sock_fd, fds);
  if (client->sock_fd >= *maxfd) {
    *maxfd = client->sock_fd + 1;
  }

  for (vector = 0; vector < client->local.vectors_count; vector++) {
    fd = client->local.vectors[vector];
    FD_SET(fd, fds);
    if (fd >= *maxfd) {
      *maxfd = fd + 1;
    }
  }
}

/* handle events from eventfd: just print a message on notification */
static int ivshmem_client_handle_event(IvshmemClient *client, const fd_set *cur,
                                       int maxfd) {
  IvshmemClientPeer *peer, other_peer;
  uint64_t kick;
  unsigned i;
  int ret;

  peer = &client->local;

  for (i = 0; i < IVSHMEM_MAX_VECTORS; i++) {
    if (peer->vectors[i] >= maxfd || !FD_ISSET(peer->vectors[i], cur)) {
      continue;
    }
    ret = read(peer->vectors[i], &kick, sizeof(kick));
    if (ret < 0) {
      return ret;
    }

    if (ret != sizeof(kick)) {
      IVSHMEM_CLIENT_DEBUG(client, "invalid read size = %d\n", ret);
      errno = EINVAL;
      return -1;
    }
    IVSHMEM_CLIENT_DEBUG(client,
                         "received event on fd %d vector %d: %" PRIu64 "\n",
                         peer->vectors[i], i, kick);
    other_peer.id = i;
    if (client->notif_cb != NULL) {
      client->notif_cb(client, &other_peer, i, client->notif_arg);
    }
  }

  return 0;
}

/* read and handle new messages on the given fd_set */
int ivshmem_client_handle_fds(IvshmemClient *client, fd_set *fds, int maxfd) {
  if (client->sock_fd < maxfd && FD_ISSET(client->sock_fd, fds) &&
      ivshmem_client_handle_server_msg(client) < 0 && errno != EINTR) {
    IVSHMEM_CLIENT_DEBUG(client, "ivshmem_client_handle_server_msg() "
                                 "failed\n");
    return -1;
  } else if (ivshmem_client_handle_event(client, fds, maxfd) < 0 &&
             errno != EINTR) {
    IVSHMEM_CLIENT_DEBUG(client, "ivshmem_client_handle_event() failed\n");
    return -1;
  }

  return 0;
}

/* send a notification on a vector of a peer */
int ivshmem_client_notify(const IvshmemClient *client,
                          const IvshmemClientPeer *peer, unsigned vector) {
  uint64_t kick = 1;
  int ret = ivshmem_client_notify_value(client, peer, vector, kick);
  return ret;
}

/* send a notification on a vector of a peer with value */
int ivshmem_client_notify_value(const IvshmemClient *client,
                                const IvshmemClientPeer *peer, unsigned vector,
                                uint64_t value) {
  int fd;

  if (vector >= IVSHMEM_MAX_VECTORS) {
    IVSHMEM_CLIENT_DEBUG(client, "invalid vector %u on peer %" PRId64 "\n",
                         vector, peer->id);
    return -1;
  }

  while (peer->vectors[vector] < 1) {
    usleep(100);
  }

  fd = peer->vectors[vector];
  IVSHMEM_CLIENT_DEBUG(client, "notify peer %" PRId64 " on vector %d, fd %d\n",
                       peer->id, vector, fd);

  if (write(fd, &value, sizeof(value)) != sizeof(value)) {
    fprintf(stderr, "could not write to %d: %s\n", peer->vectors[vector],
            strerror(errno));
    return -1;
  }
  return 0;
}

/* send a notification to all vectors of a peer */
int ivshmem_client_notify_all_vects(const IvshmemClient *client,
                                    const IvshmemClientPeer *peer) {
  unsigned vector;
  int ret = 0;

  for (vector = 0; vector < peer->vectors_count; vector++) {
    if (ivshmem_client_notify(client, peer, vector) < 0) {
      ret = -1;
    }
  }

  return ret;
}

/* send a notification to all peers */
int ivshmem_client_notify_broadcast(const IvshmemClient *client) {
  IvshmemClientPeer *peer;
  int ret = 0;

  QTAILQ_FOREACH(peer, &client->peer_list, next) {
    if (ivshmem_client_notify_value(client, peer, client->local.id,
                                    client->local.id) < 0) {
      ret = -1;
    }
  }

  return ret;
}

/* lookup peer from its id */
IvshmemClientPeer *ivshmem_client_search_peer(IvshmemClient *client,
                                              uint64_t peer_id) {
  IvshmemClientPeer *peer;

  if (peer_id == client->local.id) {
    return &client->local;
  }

  QTAILQ_FOREACH(peer, &client->peer_list, next) {
    if (peer->id == peer_id) {
      return peer;
    }
  }
  return NULL;
}

/* dump our info, the list of peers their vectors on stdout */
void ivshmem_client_dump(const IvshmemClient *client) {
  const IvshmemClientPeer *peer;
  unsigned vector;

  /* dump local infos */
  peer = &client->local;
  printf("our_id = %" PRId64 "\n", peer->id);
  for (vector = 0; vector < peer->vectors_count; vector++) {
    printf("  vector %d is enabled (fd=%d)\n", vector, peer->vectors[vector]);
  }

  /* dump peers */
  QTAILQ_FOREACH(peer, &client->peer_list, next) {
    printf("peer_id = %" PRId64 "\n", peer->id);

    for (vector = 0; vector < peer->vectors_count; vector++) {
      printf("  vector %d is enabled (fd=%d)\n", vector, peer->vectors[vector]);
    }
  }
}

/* Write message to shared memory region */
int ivshmem_client_write_msg(uint32_t base_addr, void *msg, size_t msg_size,
                             uint32_t offset) {
  int ret = 0;

  if (base_addr == 0U) {
    printf("memory is invalid\n");
    ret = 1;
  } else {
    void *buffer = (void *)(base_addr + offset);
    // Clear the buffer
    memset(buffer, 0, msg_size);
    // Write to shared memory
    memcpy(buffer, msg, msg_size);
  }
  return ret;
}

int ivshmem_client_read_msg(uint32_t base_addr, void *msg, size_t msg_size,
                            uint32_t offset) {
  int ret = 0;

  if (base_addr == 0U) {
    printf("memory is invalid\n");
    ret = 1;
  } else {
    void *buffer = (void *)(base_addr + offset);
    // Read from shared memory
    memcpy(msg, buffer, msg_size);
  }
  return ret;
}

void ivshmem_client_get_vring_info(uint32_t self_id, uint32_t remote_id,
                                   Vring_Info *vring_info) {
  uint32_t cnt = 0, a, b, i;

  if (remote_id > self_id) {
    a = self_id;
    b = remote_id;
  } else {
    a = remote_id;
    b = self_id;
  }

  for (i = 0; i < a; i++) {
    cnt += (IPC_MAX_PROCS - i - 1U);
  }
  cnt += (b - a - 1U);
  cnt *= 4U;

  if (remote_id > self_id) {
    vring_info->daTxOffset = (uint32_t)(cnt * IPC_VRING_SIZE);
    vring_info->daRxOffset = (uint32_t)((cnt + 2U) * IPC_VRING_SIZE);
    vring_info->txNotifyId = 1U;
    vring_info->rxNotifyId = 0U;
  } else {
    vring_info->daTxOffset = (uint32_t)((cnt + 2U) * IPC_VRING_SIZE);
    vring_info->daRxOffset = (uint32_t)(cnt * IPC_VRING_SIZE);
    vring_info->txNotifyId = 0U;
    vring_info->rxNotifyId = 1U;
  }
  printf("self_id=%u, remote_id=%u, count=%u\n", self_id, remote_id, cnt);
  printf("Vring_Info: daTx=0x%08x, daRx=0x%08x, txNotifyId=%u, rxNotifyId=%u\n",
         vring_info->daTxOffset, vring_info->daRxOffset, vring_info->txNotifyId,
         vring_info->rxNotifyId);
}

void ivshmem_client_get_mailbox_info(uint32_t self_id, uint32_t remote_id,
                                     Mailbox_Info *mailbox_info) {
  if ((self_id < IPC_MAX_PROCS) && (remote_id < IPC_MAX_PROCS)) {
    Ipc_MailboxInfo *pMailboxInfo = &g_IPC_MailboxInfo[self_id][remote_id];
    mailbox_info->clusterId = pMailboxInfo->rx.cluster;
    mailbox_info->userId = pMailboxInfo->rx.user;
    mailbox_info->queueId = pMailboxInfo->rx.fifo; 
  } else {
    printf("self_id = %d, remote_id = %d invalid\n", self_id, remote_id);
  }

  if (mailbox_info->clusterId < IPC_MAILBOX_CLUSTER_CNT) {
    mailbox_info->baseAddr = g_IPC_Mailbox_BasePhyAddr[mailbox_info->clusterId];
    mailbox_info->msgOffset = CSL_MAILBOX_MESSAGE(mailbox_info->queueId);
    mailbox_info->irqStatusClrOffset = CSL_MAILBOX_USER_IRQ_STATUS_CLR(mailbox_info->queueId);
    mailbox_info->irqEnableSetOffset = CSL_MAILBOX_USER_IRQ_ENABLE_SET(mailbox_info->queueId);
    mailbox_info->irqEnableClrOffset = CSL_MAILBOX_USER_IRQ_ENABLE_CLR(mailbox_info->queueId);
    dump_Mailbox_Info(mailbox_info);
  } else {
    printf("cluster_id = %d invalid\n", mailbox_info->clusterId);
  }

  return;
}
