// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (C) 2021 Texas Instruments Incorporated - https://www.ti.com/
 *
 * Common Processor Board: https://www.ti.com/tool/J721EXCPXEVM
 */

/dts-v1/;

#include "k3-j721s2-som-p0.dtsi"
#include <dt-bindings/net/ti-dp83867.h>
#include <dt-bindings/phy/phy-cadence.h>
#include <dt-bindings/phy/phy.h>
#include <dt-bindings/mux/ti-serdes.h>

/ {
	compatible = "ti,j721s2-evm", "ti,j721s2";
	model = "Texas Instruments J721S2 EVM";

	aliases {
		serial2 = &main_uart8;
		mmc0 = &main_sdhci0;
		mmc1 = &main_sdhci1;
		can0 = &main_mcan16;
		can1 = &mcu_mcan0;
		can2 = &mcu_mcan1;
		can3 = &main_mcan3;
		can4 = &main_mcan5;
		ethernet1 = &main_cpsw_port1;
	};

	chosen {
		stdout-path = "serial2:115200n8";
		bootargs = "console=ttyS2,115200n8 earlycon=ns16550a,mmio32,0x2880000 root=/dev/mmcblk1p2 rw rootfstype=ext4 rootwait";
	};

	evm_12v0: fixedregulator-evm12v0 {
		/* main supply */
		compatible = "regulator-fixed";
		regulator-name = "evm_12v0";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vsys_3v3: fixedregulator-vsys3v3 {
		/* Output of LM5140 */
		compatible = "regulator-fixed";
		regulator-name = "vsys_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&evm_12v0>;
		regulator-always-on;
		regulator-boot-on;
	};

	vsys_5v0: fixedregulator-vsys5v0 {
		/* Output of LM5140 */
		compatible = "regulator-fixed";
		regulator-name = "vsys_5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&evm_12v0>;
		regulator-always-on;
		regulator-boot-on;
	};

	vdd_mmc1: fixedregulator-sd {
		/* Output of TPS22918 */
		compatible = "regulator-fixed";
		regulator-name = "vdd_mmc1";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		enable-active-high;
		vin-supply = <&vsys_3v3>;
		gpio = <&exp2 2 GPIO_ACTIVE_HIGH>;
	};

	vdd_sd_dv: gpio-regulator-TLV71033 {
		/* Output of TLV71033 */
		compatible = "regulator-gpio";
		regulator-name = "tlv71033";
		pinctrl-names = "default";
		pinctrl-0 = <&vdd_sd_dv_pins_default>;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		vin-supply = <&vsys_5v0>;
		gpios = <&main_gpio0 8 GPIO_ACTIVE_HIGH>;
		states = <1800000 0x0>,
			 <3300000 0x1>;
	};

	transceiver1: can-phy1 {
		compatible = "ti,tcan1043";
		#phy-cells = <0>;
		max-bitrate = <5000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&mcu_mcan0_gpio_pins_default>;
		standby-gpios = <&wkup_gpio0 69 GPIO_ACTIVE_LOW>;
		enable-gpios = <&wkup_gpio0 0 GPIO_ACTIVE_HIGH>;
	};

	transceiver2: can-phy2 {
		compatible = "ti,tcan1042";
		#phy-cells = <0>;
		max-bitrate = <5000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&mcu_mcan1_gpio_pins_default>;
		standby-gpios = <&wkup_gpio0 2 GPIO_ACTIVE_HIGH>;
	};

	transceiver3: can-phy3 {
		compatible = "ti,tcan1043";
		#phy-cells = <0>;
		max-bitrate = <5000000>;
		standby-gpios = <&exp2 7 GPIO_ACTIVE_LOW>;
		enable-gpios = <&exp2 6 GPIO_ACTIVE_HIGH>;
		mux-states = <&mux0 1>;
	};

	transceiver4: can-phy4 {
		compatible = "ti,tcan1042";
		#phy-cells = <0>;
		max-bitrate = <5000000>;
		standby-gpios = <&exp_som 7 GPIO_ACTIVE_HIGH>;
		mux-states = <&mux1 1>;
	};

	dp0_pwr_3v3: fixedregulator-dp0-prw {
		compatible = "regulator-fixed";
		regulator-name = "dp0-pwr";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&exp4 0 0>;	/* P0 - DP0_PWR_SW_EN */
		enable-active-high;
	};

	dp1_pwr_3v3: regulator-dp1-prw {
		compatible = "regulator-fixed";
		regulator-name = "dp1-pwr";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&exp4 1 GPIO_ACTIVE_HIGH>; /* P1 - DP1_PWR_SW_EN */
		enable-active-high;
		regulator-always-on;
	};

	dp0: dp0-connector {
		compatible = "dp-connector";
		label = "DP0";
		type = "full-size";
		dp-pwr-supply = <&dp0_pwr_3v3>;

		port {
			dp0_connector_in: endpoint {
				remote-endpoint = <&dp0_out>;
			};
		};
	};

	panel {
		compatible = "ti,panel-edp";
		power-supply = <&dp1_pwr_3v3>;

		port {
			dp1_panel_in: endpoint {
				remote-endpoint = <&dp1_out>;
			};
		};
	};
};

&main_i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_i2c4_pins_default>;
	clock-frequency = <400000>;

	exp4: gpio@20 {
		compatible = "ti,tca6408";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&main_pmx0 {
	main_uart8_pins_default: main-uart8-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x040, PIN_INPUT, 14) /* (AC28) MCASP0_AXR0.UART8_CTSn */
			J721S2_IOPAD(0x044, PIN_OUTPUT, 14) /* (Y26) MCASP0_AXR1.UART8_RTSn */
			J721S2_IOPAD(0x0d0, PIN_INPUT, 11) /* (AF26) SPI0_CS1.UART8_RXD */
			J721S2_IOPAD(0x0d4, PIN_OUTPUT, 11) /* (AH27) SPI0_CLK.UART8_TXD */
		>;
	};

	main_i2c3_pins_default: main-i2c3-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x064, PIN_INPUT_PULLUP, 13) /* (W28) MCAN0_TX.I2C3_SCL */
			J721S2_IOPAD(0x060, PIN_INPUT_PULLUP, 13) /* (AC27) MCASP2_AXR1.I2C3_SDA */
		>;
	};

	main_mmc1_pins_default: main-mmc1-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x104, PIN_INPUT, 0) /* (P23) MMC1_CLK */
			J721S2_IOPAD(0x108, PIN_INPUT, 0) /* (N24) MMC1_CMD */
			J721S2_IOPAD(0x100, PIN_INPUT, 0) /* (###) MMC1_CLKLB */
			J721S2_IOPAD(0x0fc, PIN_INPUT, 0) /* (M23) MMC1_DAT0 */
			J721S2_IOPAD(0x0f8, PIN_INPUT, 0) /* (P24) MMC1_DAT1 */
			J721S2_IOPAD(0x0f4, PIN_INPUT, 0) /* (R24) MMC1_DAT2 */
			J721S2_IOPAD(0x0f0, PIN_INPUT, 0) /* (R22) MMC1_DAT3 */
			J721S2_IOPAD(0x0e8, PIN_INPUT, 8) /* (AE25) TIMER_IO0.MMC1_SDCD */
		>;
	};

	vdd_sd_dv_pins_default: vdd-sd-dv-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x020, PIN_INPUT, 7) /* (AA23) MCAN15_RX.GPIO0_8 */
		>;
	};

	main_usbss0_pins_default: main-usbss0-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x0ec, PIN_OUTPUT, 6) /* (AG25) TIMER_IO1.USB0_DRVVBUS */
		>;
	};

	main_mcan3_pins_default: main-mcan3-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x080, PIN_INPUT, 0) /* (U26) MCASP0_AXR4.MCAN3_RX */
			J721S2_IOPAD(0x07c, PIN_OUTPUT, 0) /* (T27) MCASP0_AXR3.MCAN3_TX */
		>;
	};

	main_mcan5_pins_default: main-mcan5-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x03c, PIN_INPUT, 0) /* (U27) MCASP0_AFSX.MCAN5_RX */
			J721S2_IOPAD(0x038, PIN_OUTPUT, 0) /* (AB28) MCASP0_ACLKX.MCAN5_TX */
		>;
	};

	main_i2c4_pins_default: main-i2c4-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x014, PIN_INPUT_PULLUP, 8) /* (AD25) I2C4_SCL */
			J721S2_IOPAD(0x010, PIN_INPUT_PULLUP, 8) /* (AF28) I2C4_SDA */
		>;
	};

	dp0_pins_default: dp0-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x0b8, PIN_INPUT, 3) /* (AA24) MCASP1_ACLKX.DP0_HPD */
		>;
	};

	main_i2c5_pins_default: main-i2c5-pins-default {
		pinctrl-single,pins = <
			J721S2_IOPAD(0x01c, PIN_INPUT, 8) /* (Y24) MCAN15_TX.I2C5_SCL */
			J721S2_IOPAD(0x018, PIN_INPUT, 8) /* (W23) MCAN14_RX.I2C5_SDA */

		>;
	};
};

&wkup_pmx0 {
	mcu_cpsw_pins_default: mcu-cpsw-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x094, PIN_INPUT, 0) /* (B22) MCU_RGMII1_RD0 */
			J721S2_WKUP_IOPAD(0x090, PIN_INPUT, 0) /* (B21) MCU_RGMII1_RD1 */
			J721S2_WKUP_IOPAD(0x08c, PIN_INPUT, 0) /* (C22) MCU_RGMII1_RD2 */
			J721S2_WKUP_IOPAD(0x088, PIN_INPUT, 0) /* (D23) MCU_RGMII1_RD3 */
			J721S2_WKUP_IOPAD(0x084, PIN_INPUT, 0) /* (D22) MCU_RGMII1_RXC */
			J721S2_WKUP_IOPAD(0x06c, PIN_INPUT, 0) /* (E23) MCU_RGMII1_RX_CTL */
			J721S2_WKUP_IOPAD(0x07c, PIN_OUTPUT, 0) /* (F23) MCU_RGMII1_TD0 */
			J721S2_WKUP_IOPAD(0x078, PIN_OUTPUT, 0) /* (G22) MCU_RGMII1_TD1 */
			J721S2_WKUP_IOPAD(0x074, PIN_OUTPUT, 0) /* (E21) MCU_RGMII1_TD2 */
			J721S2_WKUP_IOPAD(0x070, PIN_OUTPUT, 0) /* (E22) MCU_RGMII1_TD3 */
			J721S2_WKUP_IOPAD(0x080, PIN_OUTPUT, 0) /* (F21) MCU_RGMII1_TXC */
			J721S2_WKUP_IOPAD(0x068, PIN_OUTPUT, 0) /* (F22) MCU_RGMII1_TX_CTL */
		>;
	};

	mcu_mdio_pins_default: mcu-mdio-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x09c, PIN_OUTPUT, 0) /* (A21) MCU_MDIO0_MDC */
			J721S2_WKUP_IOPAD(0x098, PIN_INPUT, 0) /* (A22) MCU_MDIO0_MDIO */
		>;
	};

	mcu_mcan0_pins_default: mcu-mcan0-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x0bc, PIN_INPUT, 0) /* (E28) MCU_MCAN0_RX */
			J721S2_WKUP_IOPAD(0x0b8, PIN_OUTPUT, 0) /* (E27) MCU_MCAN0_TX */
		>;
	};

	mcu_mcan1_pins_default: mcu-mcan1-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x0d4, PIN_INPUT, 0) /* (F26) WKUP_GPIO0_5.MCU_MCAN1_RX */
			J721S2_WKUP_IOPAD(0x0d0, PIN_OUTPUT, 0) /* (C23) WKUP_GPIO0_4.MCU_MCAN1_TX */
		>;
	};

	mcu_mcan0_gpio_pins_default: mcu-mcan0-gpio-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x0c0, PIN_INPUT, 7) /* (D26) WKUP_GPIO0_0 */
			J721S2_WKUP_IOPAD(0x0a8, PIN_INPUT, 7) /* (B25) MCU_SPI0_D1.WKUP_GPIO0_69 */
		>;
	};

	mcu_mcan1_gpio_pins_default: mcu-mcan1-gpio-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x0c8, PIN_INPUT, 7) /* (C28) WKUP_GPIO0_2 */
		>;
	};

	mcu_fss0_ospi1_pins_default: mcu-fss0-ospi1-pins-default {
		pinctrl-single,pins = <
			J721S2_WKUP_IOPAD(0x040, PIN_OUTPUT, 0) /* (A19) MCU_OSPI1_CLK */
			J721S2_WKUP_IOPAD(0x05c, PIN_OUTPUT, 0) /* (D20) MCU_OSPI1_CSn0 */
			J721S2_WKUP_IOPAD(0x060, PIN_OUTPUT, 0) /* (C21) MCU_OSPI1_CSn1 */
			J721S2_WKUP_IOPAD(0x04c, PIN_INPUT, 0) /* (D21) MCU_OSPI1_D0 */
			J721S2_WKUP_IOPAD(0x050, PIN_INPUT, 0) /* (G20) MCU_OSPI1_D1 */
			J721S2_WKUP_IOPAD(0x054, PIN_INPUT, 0) /* (C20) MCU_OSPI1_D2 */
			J721S2_WKUP_IOPAD(0x058, PIN_INPUT, 0) /* (A20) MCU_OSPI1_D3 */
			J721S2_WKUP_IOPAD(0x048, PIN_INPUT, 0) /* (B19) MCU_OSPI1_DQS */
			J721S2_WKUP_IOPAD(0x044, PIN_INPUT, 0) /* (B20) MCU_OSPI1_LBCLKO */
		>;
	};
};

&main_gpio2 {
	status = "disabled";
};

&main_gpio4 {
	status = "disabled";
};

&main_gpio6 {
	status = "disabled";
};

&wkup_gpio1 {
	status = "disabled";
};

&wkup_uart0 {
	status = "reserved";
};

&main_uart0 {
	status = "disabled";
};

&main_uart1 {
	status = "disabled";
};

&main_uart2 {
	status = "disabled";
};

&main_uart3 {
	status = "disabled";
};

&main_uart4 {
	status = "disabled";
};

&main_uart5 {
	status = "disabled";
};

&main_uart6 {
	status = "disabled";
};

&main_uart7 {
	status = "disabled";
};

&main_uart8 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_uart8_pins_default>;
	/* Shared with TFA on this platform */
	power-domains = <&k3_pds 357 TI_SCI_PD_SHARED>;
};

&main_uart9 {
	status = "disabled";
};

&main_i2c0 {
	clock-frequency = <400000>;

	exp1: gpio@20 {
		compatible = "ti,tca6416";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "PCIE_2L_MODE_SEL", "PCIE_2L_PERSTZ", "PCIE_2L_RC_RSTZ",
				  "PCIE_2L_EP_RST_EN", "PCIE_1L_MODE_SEL", "PCIE_1L_PERSTZ",
				  "PCIE_1L_RC_RSTZ", "PCIE_1L_EP_RST_EN", "PCIE_2L_PRSNT#",
				  "PCIE_1L_PRSNT#", "CDCI1_OE1/OE4", "CDCI1_OE2/OE3", "EXP_MUX1",
				  "EXP_MUX2", "EXP_MUX3", "GESI_EXP_PHY_RSTz";
	};

	exp2: gpio@22 {
		compatible = "ti,tca6424";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "APPLE_AUTH_RSTZ", "MLB_RSTZ", "GPIO_USD_PWR_EN", "USBC_PWR_EN",
				  "USBC_MODE_SEL1", "USBC_MODE_SEL0", "MCAN0_EN", "MCAN0_STB#",
				  "MUX_SPAREMUX_SPARE", "MCASP/TRACE_MUX_S0", "MCASP/TRACE_MUX_S1",
				  "MLB_MUX_SEL", "MCAN_MUX_SEL", "MCASP2/SPI3_MUX_SEL", "PCIe_CLKREQn_MUX_SEL",
				  "CDCI2_RSTZ", "ENET_EXP_PWRDN", "ENET_EXP_RESETZ", "ENET_I2CMUX_SEL",
				  "ENET_EXP_SPARE2", "M2PCIE_RTSZ", "USER_INPUT1", "USER_LED1", "USER_LED2";
	};
};

&main_i2c1 {
	status = "disabled";
};

&main_i2c2 {
	status = "disabled";
};

&main_i2c3 {
	status = "disabled";
};

&main_i2c5 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_i2c5_pins_default>;
	clock-frequency = <400000>;

	exp5: gpio@20 {
		compatible = "ti,tca6408";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

&main_i2c6 {
	status = "disabled";
};

&main_spi4 {
	status = "disabled";
};

&mcu_spi0 {
	status = "disabled";
};

&mcu_spi1 {
	status = "disabled";
};

&mcu_spi2 {
	status = "disabled";
};

&main_sdhci0 {
	/* eMMC */
	non-removable;
	ti,driver-strength-ohm = <50>;
	disable-wp;
};

&main_sdhci1 {
	/* SD card */
	pinctrl-0 = <&main_mmc1_pins_default>;
	pinctrl-names = "default";
	disable-wp;
	vmmc-supply = <&vdd_mmc1>;
	vqmmc-supply = <&vdd_sd_dv>;
};

&mcu_cpsw {
	pinctrl-names = "default";
	pinctrl-0 = <&mcu_cpsw_pins_default &mcu_mdio_pins_default>;
};

&davinci_mdio {
	phy0: ethernet-phy@0 {
		reg = <0>;
		ti,rx-internal-delay = <DP83867_RGMIIDCTL_2_00_NS>;
		ti,fifo-depth = <DP83867_PHYCR_FIFO_DEPTH_4_B_NIB>;
		ti,min-output-impedance;
	};
};

&cpsw_port1 {
	phy-mode = "rgmii-rxid";
	phy-handle = <&phy0>;
};

&serdes_ln_ctrl {
	idle-states = <J721S2_SERDES0_LANE0_PCIE1_LANE0>, <J721S2_SERDES0_LANE1_USB>,
		      <J721S2_SERDES0_LANE2_EDP_LANE2>, <J721S2_SERDES0_LANE3_EDP_LANE3>;
};

&serdes_refclk {
	clock-frequency = <100000000>;
};

&serdes0 {
	serdes0_pcie_link: phy@0 {
		reg = <0>;
		cdns,num-lanes = <1>;
		#phy-cells = <0>;
		cdns,phy-type = <PHY_TYPE_PCIE>;
		resets = <&serdes_wiz0 1>;
	};
};

&usb_serdes_mux {
	idle-states = <1>; /* USB0 to SERDES lane 1 */
};

&edp_serdes_mux {
	idle-states = <1>; /* EDP0 to SERDES lane 2/3 */
};

&usbss0 {
	pinctrl-0 = <&main_usbss0_pins_default>;
	pinctrl-names = "default";
	ti,vbus-divider;
	ti,usb2-only;
};

&usb0 {
	dr_mode = "otg";
	maximum-speed = "high-speed";
};

&pcie1_rc {
	reset-gpios = <&exp1 2 GPIO_ACTIVE_HIGH>;
	phys = <&serdes0_pcie_link>;
	phy-names = "pcie-phy";
	num-lanes = <1>;
};

&pcie1_ep {
	phys = <&serdes0_pcie_link>;
	phy-names = "pcie-phy";
	num-lanes = <1>;
	status = "disabled";
};

&ospi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcu_fss0_ospi1_pins_default>;

	flash@0{
		compatible = "jedec,spi-nor";
		reg = <0x0>;
		spi-tx-bus-width = <1>;
		spi-rx-bus-width = <4>;
		spi-max-frequency = <40000000>;
		cdns,tshsl-ns = <60>;
		cdns,tsd2d-ns = <60>;
		cdns,tchsh-ns = <60>;
		cdns,tslch-ns = <60>;
		cdns,read-delay = <2>;
		#address-cells = <1>;
		#size-cells = <1>;
	};
};

&tscadc0 {
	status = "okay";
	adc {
		ti,adc-channels = <0 1 2 3 4 5 6 7>;
	};
};

&tscadc1 {
	status = "okay";
	adc {
		ti,adc-channels = <0 1 2 3 4 5 6 7>;
	};
};

&mcu_mcan0 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcu_mcan0_pins_default>;
	phys = <&transceiver1>;
};

&mcu_mcan1 {
	pinctrl-names = "default";
	pinctrl-0 = <&mcu_mcan1_pins_default>;
	phys = <&transceiver2>;
};

&dss {
	/*
	 * These clock assignments are chosen to enable the following outputs:
	 *
	 * VP0 - DisplayPort SST
	 * VP1 - DPI0
	 * VP2 - DSI
	 * VP3 - DPI1
	 */

	assigned-clocks = <&k3_clks 158 2>,
			  <&k3_clks 158 5>,
			  <&k3_clks 158 14>,
			  <&k3_clks 158 18>;
	assigned-clock-parents = <&k3_clks 158 3>,
				 <&k3_clks 158 7>,
				 <&k3_clks 158 16>,
				 <&k3_clks 158 22>;
};

&dss_ports {
	#address-cells = <1>;
	#size-cells = <0>;

	port@0 {
		reg = <0>;
		dpi0_out: endpoint {
			remote-endpoint = <&dp0_in>;
		};
	};

	port@2 {
		reg = <2>;
		dpi2_out: endpoint {
			remote-endpoint = <&dsi0_in>;
		};
	};
};

&dsi0_ports {
	port@0 {
		reg = <0>;
		dsi0_out: endpoint {
			remote-endpoint = <&dp1_in>;
		};
	};

	port@1 {
		reg = <1>;
		dsi0_in: endpoint {
			remote-endpoint = <&dpi2_out>;
		};
	};
};

&dsi_edp_bridge_ports {
	port@0 {
		reg = <0>;
		dp1_in: endpoint {
			remote-endpoint = <&dsi0_out>;
		};
	};

	port@1 {
		reg = <1>;
		dp1_out: endpoint {
			remote-endpoint = <&dp1_panel_in>;
		};
	};
};

&mhdp {
	pinctrl-names = "default";
	pinctrl-0 = <&dp0_pins_default>;
	cdns,no-hpd;
};

&dp0_ports {
	#address-cells = <1>;
	#size-cells = <0>;

	port@0 {
		reg = <0>;
		dp0_in: endpoint {
			remote-endpoint = <&dpi0_out>;
		};
	};

	port@4 {
		reg = <4>;
		dp0_out: endpoint {
			remote-endpoint = <&dp0_connector_in>;
		};
	};
};

&main_mcan0 {
	status = "disabled";
};

&main_mcan1 {
	status = "disabled";
};

&main_mcan2 {
	status = "disabled";
};

&main_mcan3 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_mcan3_pins_default>;
	phys = <&transceiver3>;
};

&main_mcan4 {
	status = "disabled";
};

&main_mcan5 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_mcan5_pins_default>;
	phys = <&transceiver4>;
};

&main_mcan6 {
	status = "disabled";
};

&main_mcan7 {
	status = "disabled";
};

&main_mcan8 {
	status = "disabled";
};

&main_mcan9 {
	status = "disabled";
};

&main_mcan10 {
	status = "disabled";
};

&main_mcan11 {
	status = "disabled";
};

&main_mcan12 {
	status = "disabled";
};

&main_mcan13 {
	status = "disabled";
};

&main_mcan14 {
	status = "disabled";
};

&main_mcan15 {
	status = "disabled";
};

&main_mcan17 {
	status = "disabled";
};

&main_cpsw {
	status = "disabled";
};

&csi0_port0 {
	status = "disabled";
};

&csi0_port1 {
	status = "disabled";
};

&csi0_port2 {
	status = "disabled";
};

&csi0_port3 {
	status = "disabled";
};

&csi0_port4 {
	status = "disabled";
};

&csi1_port0 {
	status = "disabled";
};

&csi1_port1 {
	status = "disabled";
};

&csi1_port2 {
	status = "disabled";
};

&csi1_port3 {
	status = "disabled";
};

&csi1_port4 {
	status = "disabled";
};

&wkup_i2c0 {
	status = "okay";
	tps6594x: tps6594x@48 {
		compatible = "ti,tps6594x";
		reg = <0x48>;
		ti,system-power-controller;

		rtc {
			compatible = "ti,tps6594x-rtc";
		};

		gpio {
			compatible = "ti,tps6594x-gpio";
		};
	};
};
