/* Copyright (c) 2019, Texas Instruments Incorporated
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON>QUE<PERSON>IA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */

/*
 * This file was generated by the Jacinto7_DDRSS_RegConfigTool, Revision: 0.10.0
 * This file was generated on 01/18/2024
 * This file was modified by j7_lp4_opt_v1.pl!
 */

#define DDRSS_PLL_FHS_CNT 5U
#define DDRSS_PLL_FREQUENCY_0 27500000
#define DDRSS_PLL_FREQUENCY_1 1066500000
#define DDRSS_PLL_FREQUENCY_2 1066500000

#define DDRSS_CTL_REG_INIT_COUNT (459U)
#define DDRSS_PHY_INDEP_REG_INIT_COUNT (300U)
#define DDRSS_PHY_REG_INIT_COUNT (1423U)

#define MULTI_DDR_CFG_INTRLV_GRAN 0
#define MULTI_DDR_CFG_INTRLV_SIZE 12
#define MULTI_DDR_CFG_ECC_ENABLE 0
#define MULTI_DDR_CFG_HYBRID_SELECT 24
#define MULTI_DDR_CFG_EMIFS_ACTIVE 15

uint32_t DDRSS0_ctlReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002AF8U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x01010000U,
    0x02011001U,
    0x02010000U,
    0x00020100U,
    0x0000000BU,
    0x0000001CU,
    0x00000000U,
    0x00000000U,
    0x03020200U,
    0x00005656U,
    0x00100000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x040C0000U,
    0x12481248U,
    0x00050804U,
    0x09040008U,
    0x15000204U,
    0x1760008BU,
    0x1500422BU,
    0x1760008BU,
    0x2000422BU,
    0x000A0A09U,
    0x040003C5U,
    0x1E161104U,
    0x1000922CU,
    0x1E161110U,
    0x1000922CU,
    0x02030410U,
    0x2C040500U,
    0x08292C29U,
    0x14000E0AU,
    0x04010A0AU,
    0x01010004U,
    0x04545408U,
    0x04313104U,
    0x00003131U,
    0x00010100U,
    0x03010000U,
    0x00001508U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x00001035U,
    0x00000005U,
    0x00050000U,
    0x00CB0012U,
    0x00CB0408U,
    0x00400408U,
    0x00120103U,
    0x00100005U,
    0x2F080010U,
    0x0505012FU,
    0x0401030AU,
    0x041E100BU,
    0x100B0401U,
    0x0001041EU,
    0x00160016U,
    0x033B033BU,
    0x033B033BU,
    0x03050505U,
    0x03010303U,
    0x200B100BU,
    0x04041004U,
    0x200B100BU,
    0x04041004U,
    0x03010000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x80104002U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x00050000U,
    0x00000004U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x00000000U,
    0x000002B5U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0B030500U,
    0x00040B04U,
    0x0A090000U,
    0x0A090701U,
    0x0900000EU,
    0x0907010AU,
    0x00000E0AU,
    0x07010A09U,
    0x000E0A09U,
    0x07000401U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x08080000U,
    0x01000000U,
    0x800000C0U,
    0x800000C0U,
    0x800000C0U,
    0x00000000U,
    0x00001500U,
    0x00000000U,
    0x00000001U,
    0x00000002U,
    0x0000100EU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000B0000U,
    0x000E0006U,
    0x000E0404U,
    0x00D601ABU,
    0x10100216U,
    0x01AB0216U,
    0x021600D6U,
    0x02161010U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3FF40084U,
    0x33003FF4U,
    0x00003333U,
    0x35000000U,
    0x27270035U,
    0x0F0F0000U,
    0x16000000U,
    0x00841616U,
    0x3FF43FF4U,
    0x33333300U,
    0x00000000U,
    0x00353500U,
    0x00002727U,
    0x00000F0FU,
    0x16161600U,
    0x00000020U,
    0x00000000U,
    0x00000001U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01080101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00001000U,
    0x006403E8U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15110000U,
    0x00040C18U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0xC0000000U,
    0xF000F000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000200U,
    0x00370040U,
    0x00020008U,
    0x00400100U,
    0x00400855U,
    0x01000200U,
    0x08550040U,
    0x00000040U,
    0x006B0003U,
    0x0100006BU,
    0x03030303U,
    0x00000000U,
    0x00000202U,
    0x00001FFFU,
    0x3FFF2000U,
    0x03FF0000U,
    0x000103FFU,
    0x0FFF0B00U,
    0x01010001U,
    0x01010101U,
    0x01180101U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040101U,
    0x04010100U,
    0x00000000U,
    0x00000000U,
    0x03030300U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00020201U,
    0x01000101U,
    0x01010001U,
    0x00010101U,
    0x050A0A03U,
    0x10081F1FU,
    0x00090310U,
    0x0B0C030FU,
    0x0B0C0306U,
    0x0C090006U,
    0x0100000CU,
    0x08040801U,
    0x00000004U,
    0x00000000U,
    0x00010000U,
    0x00280D00U,
    0x00000001U,
    0x00030001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00010100U,
    0x03030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000556AAU,
    0x000AAAAAU,
    0x000AA955U,
    0x00055555U,
    0x000B3133U,
    0x0004CD33U,
    0x0004CECCU,
    0x000B32CCU,
    0x00010300U,
    0x03000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000404U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3A3A1B00U,
    0x000A0000U,
    0x000000C6U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000252U,
    0x000007BCU,
    0x00000204U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x00000E15U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x02020E15U,
    0x03030202U,
    0x00000022U,
    0x00000000U,
    0x00000000U,
    0x00001403U,
    0x000007D0U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x0007001FU,
    0x001B0033U,
    0x001B0033U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01000404U,
    0x0B1E0B1EU,
    0x00000105U,
    0x00010101U,
    0x00010101U,
    0x00010001U,
    0x00000101U,
    0x02000201U,
    0x02010000U,
    0x00000200U,
    0x28060000U,
    0x00000128U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
};

uint32_t DDRSS0_phyIndepReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000101U,
    0x00640000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x00010001U,
    0x0800000FU,
    0x00000103U,
    0x00000005U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00280A00U,
    0x00000000U,
    0x0F000000U,
    0x00003200U,
    0x00000000U,
    0x00000000U,
    0x01010102U,
    0x00000000U,
    0x000000AAU,
    0x00000055U,
    0x000000B5U,
    0x0000004AU,
    0x00000056U,
    0x000000A9U,
    0x000000A9U,
    0x000000B5U,
    0x00000000U,
    0x00000000U,
    0x000F0F00U,
    0x0000001BU,
    0x000007D0U,
    0x00000300U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x00000000U,
    0x00030000U,
    0x0F000000U,
    0x00000017U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0A0A140AU,
    0x10020201U,
    0x00020805U,
    0x01000404U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x0002020FU,
    0x00340000U,
    0x00000000U,
    0x00000000U,
    0x0000FFFFU,
    0x01000000U,
    0x00080000U,
    0x02000200U,
    0x01000100U,
    0x01000000U,
    0x02000200U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000400U,
    0x02010000U,
    0x00080003U,
    0x00080000U,
    0x00000001U,
    0x00000000U,
    0x0000AA00U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000008U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x0000000AU,
    0x00000019U,
    0x00000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010003U,
    0x02000101U,
    0x01030001U,
    0x00010400U,
    0x06000105U,
    0x01070001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000401U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x2B2B0200U,
    0x00000034U,
    0x00000064U,
    0x00020064U,
    0x02000200U,
    0x48120C04U,
    0x00154812U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x04001035U,
    0x01010404U,
    0x00001500U,
    0x00150015U,
    0x01000100U,
    0x00000100U,
    0x00000000U,
    0x01010101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15040000U,
    0x0E0E0215U,
    0x00040402U,
    0x000D0035U,
    0x00218049U,
    0x00218049U,
    0x01000101U,
    0x0004000EU,
    0x00040216U,
    0x01000216U,
    0x000F000FU,
    0x02170100U,
    0x01000217U,
    0x02170217U,
    0x32103200U,
    0x01013210U,
    0x0A070601U,
    0x1F130A0DU,
    0x1F130A14U,
    0x0000C014U,
    0x00C01000U,
    0x00C01000U,
    0x00021000U,
    0x0024000EU,
    0x00240216U,
    0x00110216U,
    0x32000056U,
    0x00000301U,
    0x005B0036U,
    0x03013212U,
    0x00003600U,
    0x3212005BU,
    0x09000001U,
    0x04010504U,
    0x04000364U,
    0x0A032001U,
    0x2C31110AU,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x2C311116U,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x0000C616U,
    0x000007BCU,
    0x0000206AU,
    0x00014424U,
    0x0000206AU,
    0x00014424U,
    0x033B0016U,
    0x0303033BU,
    0x002AF803U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x00000016U,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0000033BU,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0100033BU,
    0x00370040U,
    0x00010008U,
    0x08550040U,
    0x00010040U,
    0x08550040U,
    0x00000340U,
    0x006B006BU,
    0x08040404U,
    0x00000055U,
    0x55083C5AU,
    0x5A000000U,
    0x0055083CU,
    0x3C5A0000U,
    0x00005508U,
    0x0C3C5A00U,
    0x080F0E0DU,
    0x000B0A09U,
    0x00030201U,
    0x01000000U,
    0x04020201U,
    0x00080804U,
    0x00000000U,
    0x00000000U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00000000U,
};

uint32_t DDRSS0_phyReg[] = {
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00400000U,
    0x00000080U,
    0x00DCBA98U,
    0x03000000U,
    0x00200000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000002AU,
    0x00000015U,
    0x00000015U,
    0x0000002AU,
    0x00000033U,
    0x0000000CU,
    0x0000000CU,
    0x00000033U,
    0x00543210U,
    0x003F0000U,
    0x000F013FU,
    0x20202003U,
    0x00202020U,
    0x20008008U,
    0x00000810U,
    0x00000F00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000305CCU,
    0x00030000U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x42080010U,
    0x0000803EU,
    0x00000001U,
    0x01000102U,
    0x00008000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00000000U,
    0x00000000U,
    0x00050000U,
    0x04000000U,
    0x00000055U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002001U,
    0x0000400FU,
    0x50020028U,
    0x01010000U,
    0x80080001U,
    0x10200000U,
    0x00000008U,
    0x00000000U,
    0x01090E00U,
    0x00040101U,
    0x0000010FU,
    0x00000000U,
    0x00000064U,
    0x00000000U,
    0x01010000U,
    0x01080402U,
    0x01200F02U,
    0x00194280U,
    0x00000004U,
    0x00042000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000705U,
    0x00000054U,
    0x00030820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00000000U,
    0x00000074U,
    0x00000400U,
    0x00000108U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x03000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x04102006U,
    0x00041020U,
    0x01C98C98U,
    0x3F400000U,
    0x3F3F1F3FU,
    0x0000001FU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x76543210U,
    0x00010198U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040700U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00080000U,
    0x000007FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x0000FFFFU,
    0xFFFFFFF0U,
    0x030FFFFFU,
    0x01FFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0001F7C0U,
    0x00000003U,
    0x00000000U,
    0x00001142U,
    0x040207ABU,
    0x01000080U,
    0x03900390U,
    0x03900390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000005U,
    0x01813FCCU,
    0x000000CCU,
    0x0C000DFFU,
    0x30000DFFU,
    0x3F0DFF11U,
    0x000100F0U,
    0x780DFFCCU,
    0x00007E31U,
    0x000CBF11U,
    0x01990010U,
    0x000CBF11U,
    0x01990010U,
    0x3F0DFF11U,
    0x00EF00F0U,
    0x3F0DFF11U,
    0x01FF00F0U,
    0x20040006U,
};

uint32_t DDRSS1_ctlReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002AF8U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x01010000U,
    0x02011001U,
    0x02010000U,
    0x00020100U,
    0x0000000BU,
    0x0000001CU,
    0x00000000U,
    0x00000000U,
    0x03020200U,
    0x00005656U,
    0x00100000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x040C0000U,
    0x12481248U,
    0x00050804U,
    0x09040008U,
    0x15000204U,
    0x1760008BU,
    0x1500422BU,
    0x1760008BU,
    0x2000422BU,
    0x000A0A09U,
    0x040003C5U,
    0x1E161104U,
    0x1000922CU,
    0x1E161110U,
    0x1000922CU,
    0x02030410U,
    0x2C040500U,
    0x08292C29U,
    0x14000E0AU,
    0x04010A0AU,
    0x01010004U,
    0x04545408U,
    0x04313104U,
    0x00003131U,
    0x00010100U,
    0x03010000U,
    0x00001508U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x00001035U,
    0x00000005U,
    0x00050000U,
    0x00CB0012U,
    0x00CB0408U,
    0x00400408U,
    0x00120103U,
    0x00100005U,
    0x2F080010U,
    0x0505012FU,
    0x0401030AU,
    0x041E100BU,
    0x100B0401U,
    0x0001041EU,
    0x00160016U,
    0x033B033BU,
    0x033B033BU,
    0x03050505U,
    0x03010303U,
    0x200B100BU,
    0x04041004U,
    0x200B100BU,
    0x04041004U,
    0x03010000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x80104002U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x00050000U,
    0x00000004U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x00000000U,
    0x000002B5U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0B030500U,
    0x00040B04U,
    0x0A090000U,
    0x0A090701U,
    0x0900000EU,
    0x0907010AU,
    0x00000E0AU,
    0x07010A09U,
    0x000E0A09U,
    0x07000401U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x08080000U,
    0x01000000U,
    0x800000C0U,
    0x800000C0U,
    0x800000C0U,
    0x00000000U,
    0x00001500U,
    0x00000000U,
    0x00000001U,
    0x00000002U,
    0x0000100EU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000B0000U,
    0x000E0006U,
    0x000E0404U,
    0x00D601ABU,
    0x10100216U,
    0x01AB0216U,
    0x021600D6U,
    0x02161010U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3FF40084U,
    0x33003FF4U,
    0x00003333U,
    0x35000000U,
    0x27270035U,
    0x0F0F0000U,
    0x16000000U,
    0x00841616U,
    0x3FF43FF4U,
    0x33333300U,
    0x00000000U,
    0x00353500U,
    0x00002727U,
    0x00000F0FU,
    0x16161600U,
    0x00000020U,
    0x00000000U,
    0x00000001U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01080101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00001000U,
    0x006403E8U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15110000U,
    0x00040C18U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0xC0000000U,
    0xF000F000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000200U,
    0x00370040U,
    0x00020008U,
    0x00400100U,
    0x00400855U,
    0x01000200U,
    0x08550040U,
    0x00000040U,
    0x006B0003U,
    0x0100006BU,
    0x03030303U,
    0x00000000U,
    0x00000202U,
    0x00001FFFU,
    0x3FFF2000U,
    0x03FF0000U,
    0x000103FFU,
    0x0FFF0B00U,
    0x01010001U,
    0x01010101U,
    0x01180101U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040101U,
    0x04010100U,
    0x00000000U,
    0x00000000U,
    0x03030300U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00020201U,
    0x01000101U,
    0x01010001U,
    0x00010101U,
    0x050A0A03U,
    0x10081F1FU,
    0x00090310U,
    0x0B0C030FU,
    0x0B0C0306U,
    0x0C090006U,
    0x0100000CU,
    0x08040801U,
    0x00000004U,
    0x00000000U,
    0x00010000U,
    0x00280D00U,
    0x00000001U,
    0x00030001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00010100U,
    0x03030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000556AAU,
    0x000AAAAAU,
    0x000AA955U,
    0x00055555U,
    0x000B3133U,
    0x0004CD33U,
    0x0004CECCU,
    0x000B32CCU,
    0x00010300U,
    0x03000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000404U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3A3A1B00U,
    0x000A0000U,
    0x000000C6U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000252U,
    0x000007BCU,
    0x00000204U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x00000E15U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x02020E15U,
    0x03030202U,
    0x00000022U,
    0x00000000U,
    0x00000000U,
    0x00001403U,
    0x000007D0U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x0007001FU,
    0x001B0033U,
    0x001B0033U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01000404U,
    0x0B1E0B1EU,
    0x00000105U,
    0x00010101U,
    0x00010101U,
    0x00010001U,
    0x00000101U,
    0x02000201U,
    0x02010000U,
    0x00000200U,
    0x28060000U,
    0x00000128U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
};

uint32_t DDRSS1_phyIndepReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000101U,
    0x00640000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x00010001U,
    0x0800000FU,
    0x00000103U,
    0x00000005U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00280A00U,
    0x00000000U,
    0x0F000000U,
    0x00003200U,
    0x00000000U,
    0x00000000U,
    0x01010102U,
    0x00000000U,
    0x000000AAU,
    0x00000055U,
    0x000000B5U,
    0x0000004AU,
    0x00000056U,
    0x000000A9U,
    0x000000A9U,
    0x000000B5U,
    0x00000000U,
    0x00000000U,
    0x000F0F00U,
    0x0000001BU,
    0x000007D0U,
    0x00000300U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x00000000U,
    0x00030000U,
    0x0F000000U,
    0x00000017U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0A0A140AU,
    0x10020201U,
    0x00020805U,
    0x01000404U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x0002020FU,
    0x00340000U,
    0x00000000U,
    0x00000000U,
    0x0000FFFFU,
    0x01000000U,
    0x00080000U,
    0x02000200U,
    0x01000100U,
    0x01000000U,
    0x02000200U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000400U,
    0x02010000U,
    0x00080003U,
    0x00080000U,
    0x00000001U,
    0x00000000U,
    0x0000AA00U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000008U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x0000000AU,
    0x00000019U,
    0x00000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010003U,
    0x02000101U,
    0x01030001U,
    0x00010400U,
    0x06000105U,
    0x01070001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000401U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x2B2B0200U,
    0x00000034U,
    0x00000064U,
    0x00020064U,
    0x02000200U,
    0x48120C04U,
    0x00154812U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x04001035U,
    0x01010404U,
    0x00001500U,
    0x00150015U,
    0x01000100U,
    0x00000100U,
    0x00000000U,
    0x01010101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15040000U,
    0x0E0E0215U,
    0x00040402U,
    0x000D0035U,
    0x00218049U,
    0x00218049U,
    0x01000101U,
    0x0004000EU,
    0x00040216U,
    0x01000216U,
    0x000F000FU,
    0x02170100U,
    0x01000217U,
    0x02170217U,
    0x32103200U,
    0x01013210U,
    0x0A070601U,
    0x1F130A0DU,
    0x1F130A14U,
    0x0000C014U,
    0x00C01000U,
    0x00C01000U,
    0x00021000U,
    0x0024000EU,
    0x00240216U,
    0x00110216U,
    0x32000056U,
    0x00000301U,
    0x005B0036U,
    0x03013212U,
    0x00003600U,
    0x3212005BU,
    0x09000001U,
    0x04010504U,
    0x04000364U,
    0x0A032001U,
    0x2C31110AU,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x2C311116U,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x0000C616U,
    0x000007BCU,
    0x0000206AU,
    0x00014424U,
    0x0000206AU,
    0x00014424U,
    0x033B0016U,
    0x0303033BU,
    0x002AF803U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x00000016U,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0000033BU,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0100033BU,
    0x00370040U,
    0x00010008U,
    0x08550040U,
    0x00010040U,
    0x08550040U,
    0x00000340U,
    0x006B006BU,
    0x08040404U,
    0x00000055U,
    0x55083C5AU,
    0x5A000000U,
    0x0055083CU,
    0x3C5A0000U,
    0x00005508U,
    0x0C3C5A00U,
    0x080F0E0DU,
    0x000B0A09U,
    0x00030201U,
    0x01000000U,
    0x04020201U,
    0x00080804U,
    0x00000000U,
    0x00000000U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00000000U,
};

uint32_t DDRSS1_phyReg[] = {
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00400000U,
    0x00000080U,
    0x00DCBA98U,
    0x03000000U,
    0x00200000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000002AU,
    0x00000015U,
    0x00000015U,
    0x0000002AU,
    0x00000033U,
    0x0000000CU,
    0x0000000CU,
    0x00000033U,
    0x00543210U,
    0x003F0000U,
    0x000F013FU,
    0x20202003U,
    0x00202020U,
    0x20008008U,
    0x00000810U,
    0x00000F00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000305CCU,
    0x00030000U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x42080010U,
    0x0000803EU,
    0x00000001U,
    0x01000102U,
    0x00008000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00000000U,
    0x00000000U,
    0x00050000U,
    0x04000000U,
    0x00000055U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002001U,
    0x0000400FU,
    0x50020028U,
    0x01010000U,
    0x80080001U,
    0x10200000U,
    0x00000008U,
    0x00000000U,
    0x01090E00U,
    0x00040101U,
    0x0000010FU,
    0x00000000U,
    0x00000064U,
    0x00000000U,
    0x01010000U,
    0x01080402U,
    0x01200F02U,
    0x00194280U,
    0x00000004U,
    0x00042000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000705U,
    0x00000054U,
    0x00030820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00000000U,
    0x00000074U,
    0x00000400U,
    0x00000108U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x03000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x04102006U,
    0x00041020U,
    0x01C98C98U,
    0x3F400000U,
    0x3F3F1F3FU,
    0x0000001FU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x76543210U,
    0x00010198U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040700U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00080000U,
    0x000007FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x0000FFFFU,
    0xFFFFFFF0U,
    0x030FFFFFU,
    0x01FFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0001F7C0U,
    0x00000003U,
    0x00000000U,
    0x00001142U,
    0x040207ABU,
    0x01000080U,
    0x03900390U,
    0x03900390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000005U,
    0x01813FCCU,
    0x000000CCU,
    0x0C000DFFU,
    0x30000DFFU,
    0x3F0DFF11U,
    0x000100F0U,
    0x780DFFCCU,
    0x00007E31U,
    0x000CBF11U,
    0x01990010U,
    0x000CBF11U,
    0x01990010U,
    0x3F0DFF11U,
    0x00EF00F0U,
    0x3F0DFF11U,
    0x01FF00F0U,
    0x20040006U,
};

uint32_t DDRSS2_ctlReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002AF8U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x01010000U,
    0x02011001U,
    0x02010000U,
    0x00020100U,
    0x0000000BU,
    0x0000001CU,
    0x00000000U,
    0x00000000U,
    0x03020200U,
    0x00005656U,
    0x00100000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x040C0000U,
    0x12481248U,
    0x00050804U,
    0x09040008U,
    0x15000204U,
    0x1760008BU,
    0x1500422BU,
    0x1760008BU,
    0x2000422BU,
    0x000A0A09U,
    0x040003C5U,
    0x1E161104U,
    0x1000922CU,
    0x1E161110U,
    0x1000922CU,
    0x02030410U,
    0x2C040500U,
    0x08292C29U,
    0x14000E0AU,
    0x04010A0AU,
    0x01010004U,
    0x04545408U,
    0x04313104U,
    0x00003131U,
    0x00010100U,
    0x03010000U,
    0x00001508U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x00001035U,
    0x00000005U,
    0x00050000U,
    0x00CB0012U,
    0x00CB0408U,
    0x00400408U,
    0x00120103U,
    0x00100005U,
    0x2F080010U,
    0x0505012FU,
    0x0401030AU,
    0x041E100BU,
    0x100B0401U,
    0x0001041EU,
    0x00160016U,
    0x033B033BU,
    0x033B033BU,
    0x03050505U,
    0x03010303U,
    0x200B100BU,
    0x04041004U,
    0x200B100BU,
    0x04041004U,
    0x03010000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x80104002U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x00050000U,
    0x00000004U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x00000000U,
    0x000002B5U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0B030500U,
    0x00040B04U,
    0x0A090000U,
    0x0A090701U,
    0x0900000EU,
    0x0907010AU,
    0x00000E0AU,
    0x07010A09U,
    0x000E0A09U,
    0x07000401U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x08080000U,
    0x01000000U,
    0x800000C0U,
    0x800000C0U,
    0x800000C0U,
    0x00000000U,
    0x00001500U,
    0x00000000U,
    0x00000001U,
    0x00000002U,
    0x0000100EU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000B0000U,
    0x000E0006U,
    0x000E0404U,
    0x00D601ABU,
    0x10100216U,
    0x01AB0216U,
    0x021600D6U,
    0x02161010U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3FF40084U,
    0x33003FF4U,
    0x00003333U,
    0x35000000U,
    0x27270035U,
    0x0F0F0000U,
    0x16000000U,
    0x00841616U,
    0x3FF43FF4U,
    0x33333300U,
    0x00000000U,
    0x00353500U,
    0x00002727U,
    0x00000F0FU,
    0x16161600U,
    0x00000020U,
    0x00000000U,
    0x00000001U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01080101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00001000U,
    0x006403E8U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15110000U,
    0x00040C18U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0xC0000000U,
    0xF000F000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000200U,
    0x00370040U,
    0x00020008U,
    0x00400100U,
    0x00400855U,
    0x01000200U,
    0x08550040U,
    0x00000040U,
    0x006B0003U,
    0x0100006BU,
    0x03030303U,
    0x00000000U,
    0x00000202U,
    0x00001FFFU,
    0x3FFF2000U,
    0x03FF0000U,
    0x000103FFU,
    0x0FFF0B00U,
    0x01010001U,
    0x01010101U,
    0x01180101U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040101U,
    0x04010100U,
    0x00000000U,
    0x00000000U,
    0x03030300U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00020201U,
    0x01000101U,
    0x01010001U,
    0x00010101U,
    0x050A0A03U,
    0x10081F1FU,
    0x00090310U,
    0x0B0C030FU,
    0x0B0C0306U,
    0x0C090006U,
    0x0100000CU,
    0x08040801U,
    0x00000004U,
    0x00000000U,
    0x00010000U,
    0x00280D00U,
    0x00000001U,
    0x00030001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00010100U,
    0x03030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000556AAU,
    0x000AAAAAU,
    0x000AA955U,
    0x00055555U,
    0x000B3133U,
    0x0004CD33U,
    0x0004CECCU,
    0x000B32CCU,
    0x00010300U,
    0x03000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000404U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3A3A1B00U,
    0x000A0000U,
    0x000000C6U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000252U,
    0x000007BCU,
    0x00000204U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x00000E15U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x02020E15U,
    0x03030202U,
    0x00000022U,
    0x00000000U,
    0x00000000U,
    0x00001403U,
    0x000007D0U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x0007001FU,
    0x001B0033U,
    0x001B0033U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01000404U,
    0x0B1E0B1EU,
    0x00000105U,
    0x00010101U,
    0x00010101U,
    0x00010001U,
    0x00000101U,
    0x02000201U,
    0x02010000U,
    0x00000200U,
    0x28060000U,
    0x00000128U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
};

uint32_t DDRSS2_phyIndepReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000101U,
    0x00640000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x00010001U,
    0x0800000FU,
    0x00000103U,
    0x00000005U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00280A00U,
    0x00000000U,
    0x0F000000U,
    0x00003200U,
    0x00000000U,
    0x00000000U,
    0x01010102U,
    0x00000000U,
    0x000000AAU,
    0x00000055U,
    0x000000B5U,
    0x0000004AU,
    0x00000056U,
    0x000000A9U,
    0x000000A9U,
    0x000000B5U,
    0x00000000U,
    0x00000000U,
    0x000F0F00U,
    0x0000001BU,
    0x000007D0U,
    0x00000300U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x00000000U,
    0x00030000U,
    0x0F000000U,
    0x00000017U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0A0A140AU,
    0x10020201U,
    0x00020805U,
    0x01000404U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x0002020FU,
    0x00340000U,
    0x00000000U,
    0x00000000U,
    0x0000FFFFU,
    0x01000000U,
    0x00080000U,
    0x02000200U,
    0x01000100U,
    0x01000000U,
    0x02000200U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000400U,
    0x02010000U,
    0x00080003U,
    0x00080000U,
    0x00000001U,
    0x00000000U,
    0x0000AA00U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000008U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x0000000AU,
    0x00000019U,
    0x00000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010003U,
    0x02000101U,
    0x01030001U,
    0x00010400U,
    0x06000105U,
    0x01070001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000401U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x2B2B0200U,
    0x00000034U,
    0x00000064U,
    0x00020064U,
    0x02000200U,
    0x48120C04U,
    0x00154812U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x04001035U,
    0x01010404U,
    0x00001500U,
    0x00150015U,
    0x01000100U,
    0x00000100U,
    0x00000000U,
    0x01010101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15040000U,
    0x0E0E0215U,
    0x00040402U,
    0x000D0035U,
    0x00218049U,
    0x00218049U,
    0x01000101U,
    0x0004000EU,
    0x00040216U,
    0x01000216U,
    0x000F000FU,
    0x02170100U,
    0x01000217U,
    0x02170217U,
    0x32103200U,
    0x01013210U,
    0x0A070601U,
    0x1F130A0DU,
    0x1F130A14U,
    0x0000C014U,
    0x00C01000U,
    0x00C01000U,
    0x00021000U,
    0x0024000EU,
    0x00240216U,
    0x00110216U,
    0x32000056U,
    0x00000301U,
    0x005B0036U,
    0x03013212U,
    0x00003600U,
    0x3212005BU,
    0x09000001U,
    0x04010504U,
    0x04000364U,
    0x0A032001U,
    0x2C31110AU,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x2C311116U,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x0000C616U,
    0x000007BCU,
    0x0000206AU,
    0x00014424U,
    0x0000206AU,
    0x00014424U,
    0x033B0016U,
    0x0303033BU,
    0x002AF803U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x00000016U,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0000033BU,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0100033BU,
    0x00370040U,
    0x00010008U,
    0x08550040U,
    0x00010040U,
    0x08550040U,
    0x00000340U,
    0x006B006BU,
    0x08040404U,
    0x00000055U,
    0x55083C5AU,
    0x5A000000U,
    0x0055083CU,
    0x3C5A0000U,
    0x00005508U,
    0x0C3C5A00U,
    0x080F0E0DU,
    0x000B0A09U,
    0x00030201U,
    0x01000000U,
    0x04020201U,
    0x00080804U,
    0x00000000U,
    0x00000000U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00000000U,
};

uint32_t DDRSS2_phyReg[] = {
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00400000U,
    0x00000080U,
    0x00DCBA98U,
    0x03000000U,
    0x00200000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000002AU,
    0x00000015U,
    0x00000015U,
    0x0000002AU,
    0x00000033U,
    0x0000000CU,
    0x0000000CU,
    0x00000033U,
    0x00543210U,
    0x003F0000U,
    0x000F013FU,
    0x20202003U,
    0x00202020U,
    0x20008008U,
    0x00000810U,
    0x00000F00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000305CCU,
    0x00030000U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x42080010U,
    0x0000803EU,
    0x00000001U,
    0x01000102U,
    0x00008000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00000000U,
    0x00000000U,
    0x00050000U,
    0x04000000U,
    0x00000055U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002001U,
    0x0000400FU,
    0x50020028U,
    0x01010000U,
    0x80080001U,
    0x10200000U,
    0x00000008U,
    0x00000000U,
    0x01090E00U,
    0x00040101U,
    0x0000010FU,
    0x00000000U,
    0x00000064U,
    0x00000000U,
    0x01010000U,
    0x01080402U,
    0x01200F02U,
    0x00194280U,
    0x00000004U,
    0x00042000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000705U,
    0x00000054U,
    0x00030820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00000000U,
    0x00000074U,
    0x00000400U,
    0x00000108U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x03000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x04102006U,
    0x00041020U,
    0x01C98C98U,
    0x3F400000U,
    0x3F3F1F3FU,
    0x0000001FU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x76543210U,
    0x00010198U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040700U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00080000U,
    0x000007FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x0000FFFFU,
    0xFFFFFFF0U,
    0x030FFFFFU,
    0x01FFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0001F7C0U,
    0x00000003U,
    0x00000000U,
    0x00001142U,
    0x040207ABU,
    0x01000080U,
    0x03900390U,
    0x03900390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000005U,
    0x01813FCCU,
    0x000000CCU,
    0x0C000DFFU,
    0x30000DFFU,
    0x3F0DFF11U,
    0x000100F0U,
    0x780DFFCCU,
    0x00007E31U,
    0x000CBF11U,
    0x01990010U,
    0x000CBF11U,
    0x01990010U,
    0x3F0DFF11U,
    0x00EF00F0U,
    0x3F0DFF11U,
    0x01FF00F0U,
    0x20040006U,
};

uint32_t DDRSS3_ctlReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002AF8U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x000681C8U,
    0x004111C9U,
    0x00000005U,
    0x000010A9U,
    0x01010000U,
    0x02011001U,
    0x02010000U,
    0x00020100U,
    0x0000000BU,
    0x0000001CU,
    0x00000000U,
    0x00000000U,
    0x03020200U,
    0x00005656U,
    0x00100000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x040C0000U,
    0x12481248U,
    0x00050804U,
    0x09040008U,
    0x15000204U,
    0x1760008BU,
    0x1500422BU,
    0x1760008BU,
    0x2000422BU,
    0x000A0A09U,
    0x040003C5U,
    0x1E161104U,
    0x1000922CU,
    0x1E161110U,
    0x1000922CU,
    0x02030410U,
    0x2C040500U,
    0x08292C29U,
    0x14000E0AU,
    0x04010A0AU,
    0x01010004U,
    0x04545408U,
    0x04313104U,
    0x00003131U,
    0x00010100U,
    0x03010000U,
    0x00001508U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x00001035U,
    0x00000005U,
    0x00050000U,
    0x00CB0012U,
    0x00CB0408U,
    0x00400408U,
    0x00120103U,
    0x00100005U,
    0x2F080010U,
    0x0505012FU,
    0x0401030AU,
    0x041E100BU,
    0x100B0401U,
    0x0001041EU,
    0x00160016U,
    0x033B033BU,
    0x033B033BU,
    0x03050505U,
    0x03010303U,
    0x200B100BU,
    0x04041004U,
    0x200B100BU,
    0x04041004U,
    0x03010000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x80104002U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x00050000U,
    0x00000004U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x000018C0U,
    0x00000000U,
    0x000002B5U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00040D40U,
    0x00000000U,
    0x00007173U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0B030500U,
    0x00040B04U,
    0x0A090000U,
    0x0A090701U,
    0x0900000EU,
    0x0907010AU,
    0x00000E0AU,
    0x07010A09U,
    0x000E0A09U,
    0x07000401U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x08080000U,
    0x01000000U,
    0x800000C0U,
    0x800000C0U,
    0x800000C0U,
    0x00000000U,
    0x00001500U,
    0x00000000U,
    0x00000001U,
    0x00000002U,
    0x0000100EU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000B0000U,
    0x000E0006U,
    0x000E0404U,
    0x00D601ABU,
    0x10100216U,
    0x01AB0216U,
    0x021600D6U,
    0x02161010U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3FF40084U,
    0x33003FF4U,
    0x00003333U,
    0x35000000U,
    0x27270035U,
    0x0F0F0000U,
    0x16000000U,
    0x00841616U,
    0x3FF43FF4U,
    0x33333300U,
    0x00000000U,
    0x00353500U,
    0x00002727U,
    0x00000F0FU,
    0x16161600U,
    0x00000020U,
    0x00000000U,
    0x00000001U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01080101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00001000U,
    0x006403E8U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15110000U,
    0x00040C18U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0xC0000000U,
    0xF000F000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xF000C000U,
    0x0000F000U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000200U,
    0x00370040U,
    0x00020008U,
    0x00400100U,
    0x00400855U,
    0x01000200U,
    0x08550040U,
    0x00000040U,
    0x006B0003U,
    0x0100006BU,
    0x03030303U,
    0x00000000U,
    0x00000202U,
    0x00001FFFU,
    0x3FFF2000U,
    0x03FF0000U,
    0x000103FFU,
    0x0FFF0B00U,
    0x01010001U,
    0x01010101U,
    0x01180101U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040101U,
    0x04010100U,
    0x00000000U,
    0x00000000U,
    0x03030300U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00020201U,
    0x01000101U,
    0x01010001U,
    0x00010101U,
    0x050A0A03U,
    0x10081F1FU,
    0x00090310U,
    0x0B0C030FU,
    0x0B0C0306U,
    0x0C090006U,
    0x0100000CU,
    0x08040801U,
    0x00000004U,
    0x00000000U,
    0x00010000U,
    0x00280D00U,
    0x00000001U,
    0x00030001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000001U,
    0x00010100U,
    0x03030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000556AAU,
    0x000AAAAAU,
    0x000AA955U,
    0x00055555U,
    0x000B3133U,
    0x0004CD33U,
    0x0004CECCU,
    0x000B32CCU,
    0x00010300U,
    0x03000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000404U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3A3A1B00U,
    0x000A0000U,
    0x000000C6U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000252U,
    0x000007BCU,
    0x00000204U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x00000E15U,
    0x0000206AU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x0000613EU,
    0x00014424U,
    0x02020E15U,
    0x03030202U,
    0x00000022U,
    0x00000000U,
    0x00000000U,
    0x00001403U,
    0x000007D0U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x0007001FU,
    0x001B0033U,
    0x001B0033U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01000404U,
    0x0B1E0B1EU,
    0x00000105U,
    0x00010101U,
    0x00010101U,
    0x00010001U,
    0x00000101U,
    0x02000201U,
    0x02010000U,
    0x00000200U,
    0x28060000U,
    0x00000128U,
    0xFFFFFFFFU,
    0xFFFFFFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
};

uint32_t DDRSS3_phyIndepReg[] = {
    0x00000B00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000101U,
    0x00640000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x00010001U,
    0x0800000FU,
    0x00000103U,
    0x00000005U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00280A00U,
    0x00000000U,
    0x0F000000U,
    0x00003200U,
    0x00000000U,
    0x00000000U,
    0x01010102U,
    0x00000000U,
    0x000000AAU,
    0x00000055U,
    0x000000B5U,
    0x0000004AU,
    0x00000056U,
    0x000000A9U,
    0x000000A9U,
    0x000000B5U,
    0x00000000U,
    0x00000000U,
    0x000F0F00U,
    0x0000001BU,
    0x000007D0U,
    0x00000300U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x00000000U,
    0x00030000U,
    0x0F000000U,
    0x00000017U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0A0A140AU,
    0x10020201U,
    0x00020805U,
    0x01000404U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x0002020FU,
    0x00340000U,
    0x00000000U,
    0x00000000U,
    0x0000FFFFU,
    0x01000000U,
    0x00080000U,
    0x02000200U,
    0x01000100U,
    0x01000000U,
    0x02000200U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000400U,
    0x02010000U,
    0x00080003U,
    0x00080000U,
    0x00000001U,
    0x00000000U,
    0x0000AA00U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000008U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x0000000AU,
    0x00000019U,
    0x00000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010003U,
    0x02000101U,
    0x01030001U,
    0x00010400U,
    0x06000105U,
    0x01070001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000401U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x2B2B0200U,
    0x00000034U,
    0x00000064U,
    0x00020064U,
    0x02000200U,
    0x48120C04U,
    0x00154812U,
    0x00000063U,
    0x0000032BU,
    0x00001035U,
    0x0000032BU,
    0x04001035U,
    0x01010404U,
    0x00001500U,
    0x00150015U,
    0x01000100U,
    0x00000100U,
    0x00000000U,
    0x01010101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15040000U,
    0x0E0E0215U,
    0x00040402U,
    0x000D0035U,
    0x00218049U,
    0x00218049U,
    0x01000101U,
    0x0004000EU,
    0x00040216U,
    0x01000216U,
    0x000F000FU,
    0x02170100U,
    0x01000217U,
    0x02170217U,
    0x32103200U,
    0x01013210U,
    0x0A070601U,
    0x1F130A0DU,
    0x1F130A14U,
    0x0000C014U,
    0x00C01000U,
    0x00C01000U,
    0x00021000U,
    0x0024000EU,
    0x00240216U,
    0x00110216U,
    0x32000056U,
    0x00000301U,
    0x005B0036U,
    0x03013212U,
    0x00003600U,
    0x3212005BU,
    0x09000001U,
    0x04010504U,
    0x04000364U,
    0x0A032001U,
    0x2C31110AU,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x2C311116U,
    0x00002918U,
    0x6000838EU,
    0x1E202008U,
    0x0000C616U,
    0x000007BCU,
    0x0000206AU,
    0x00014424U,
    0x0000206AU,
    0x00014424U,
    0x033B0016U,
    0x0303033BU,
    0x002AF803U,
    0x0001ADAFU,
    0x00000005U,
    0x0000006EU,
    0x00000016U,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0000033BU,
    0x000681C8U,
    0x0001ADAFU,
    0x00000005U,
    0x000010A9U,
    0x0100033BU,
    0x00370040U,
    0x00010008U,
    0x08550040U,
    0x00010040U,
    0x08550040U,
    0x00000340U,
    0x006B006BU,
    0x08040404U,
    0x00000055U,
    0x55083C5AU,
    0x5A000000U,
    0x0055083CU,
    0x3C5A0000U,
    0x00005508U,
    0x0C3C5A00U,
    0x080F0E0DU,
    0x000B0A09U,
    0x00030201U,
    0x01000000U,
    0x04020201U,
    0x00080804U,
    0x00000000U,
    0x00000000U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00330084U,
    0x00160000U,
    0x35333FF4U,
    0x00160F27U,
    0x35333FF4U,
    0x00160F27U,
    0x00000000U,
};

uint32_t DDRSS3_phyReg[] = {
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000004F0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800C0U,
    0x060100CCU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000AAAAU,
    0x00005555U,
    0x0000B5B5U,
    0x00004A4AU,
    0x00005656U,
    0x0000A9A9U,
    0x0000A9A9U,
    0x0000B5B5U,
    0x00000000U,
    0x00000000U,
    0x2A000000U,
    0x00000808U,
    0x0F000000U,
    0x00000F08U,
    0x10400000U,
    0x0C002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xAAAAAAAAU,
    0x55555555U,
    0xAAAAAAAAU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x07FF0000U,
    0x0080081FU,
    0x00081020U,
    0x04010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x01CC0C01U,
    0x1003CC0CU,
    0x20000140U,
    0x07FF0200U,
    0x0000DD01U,
    0x10100303U,
    0x10101010U,
    0x10101010U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x00050010U,
    0x51517041U,
    0x31C06001U,
    0x07AB01ABU,
    0x00C0C001U,
    0x0E0D0001U,
    0x10001000U,
    0x0C083E42U,
    0x0F0C3701U,
    0x01000140U,
    0x0C000420U,
    0x00000198U,
    0x0A0000D0U,
    0x00030200U,
    0x02800000U,
    0x80800000U,
    0x000E2010U,
    0x76543210U,
    0x00000008U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x02800280U,
    0x00000280U,
    0x0000A000U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x00A000A0U,
    0x01C200A0U,
    0x01A00005U,
    0x00000000U,
    0x00000000U,
    0x00080200U,
    0x00000000U,
    0x20202000U,
    0x20202020U,
    0xF0F02020U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000100U,
    0x00000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00400000U,
    0x00000080U,
    0x00DCBA98U,
    0x03000000U,
    0x00200000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000002AU,
    0x00000015U,
    0x00000015U,
    0x0000002AU,
    0x00000033U,
    0x0000000CU,
    0x0000000CU,
    0x00000033U,
    0x00543210U,
    0x003F0000U,
    0x000F013FU,
    0x20202003U,
    0x00202020U,
    0x20008008U,
    0x00000810U,
    0x00000F00U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000305CCU,
    0x00030000U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x00000300U,
    0x42080010U,
    0x0000803EU,
    0x00000001U,
    0x01000102U,
    0x00008000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010100U,
    0x00000000U,
    0x00000000U,
    0x00050000U,
    0x04000000U,
    0x00000055U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002001U,
    0x0000400FU,
    0x50020028U,
    0x01010000U,
    0x80080001U,
    0x10200000U,
    0x00000008U,
    0x00000000U,
    0x01090E00U,
    0x00040101U,
    0x0000010FU,
    0x00000000U,
    0x00000064U,
    0x00000000U,
    0x01010000U,
    0x01080402U,
    0x01200F02U,
    0x00194280U,
    0x00000004U,
    0x00042000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000705U,
    0x00000054U,
    0x00030820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00010820U,
    0x00000000U,
    0x00000074U,
    0x00000400U,
    0x00000108U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x03000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x04102006U,
    0x00041020U,
    0x01C98C98U,
    0x3F400000U,
    0x3F3F1F3FU,
    0x0000001FU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x76543210U,
    0x00010198U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040700U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00080000U,
    0x000007FFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000FFFFFU,
    0x000FFFFFU,
    0x0000FFFFU,
    0xFFFFFFF0U,
    0x030FFFFFU,
    0x01FFFFFFU,
    0x0000FFFFU,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0001F7C0U,
    0x00000003U,
    0x00000000U,
    0x00001142U,
    0x040207ABU,
    0x01000080U,
    0x03900390U,
    0x03900390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000390U,
    0x00000005U,
    0x01813FCCU,
    0x000000CCU,
    0x0C000DFFU,
    0x30000DFFU,
    0x3F0DFF11U,
    0x000100F0U,
    0x780DFFCCU,
    0x00007E31U,
    0x000CBF11U,
    0x01990010U,
    0x000CBF11U,
    0x01990010U,
    0x3F0DFF11U,
    0x00EF00F0U,
    0x3F0DFF11U,
    0x01FF00F0U,
    0x20040006U,
};

uint16_t DDRSS_ctlRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    182,
    183,
    184,
    185,
    186,
    187,
    188,
    189,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    354,
    355,
    356,
    357,
    358,
    359,
    360,
    361,
    362,
    363,
    364,
    365,
    366,
    367,
    368,
    369,
    370,
    371,
    372,
    373,
    374,
    375,
    376,
    377,
    378,
    379,
    380,
    381,
    382,
    383,
    384,
    385,
    386,
    387,
    388,
    389,
    390,
    391,
    392,
    393,
    394,
    395,
    396,
    397,
    398,
    399,
    400,
    401,
    402,
    403,
    404,
    405,
    406,
    407,
    408,
    409,
    410,
    411,
    412,
    413,
    414,
    415,
    416,
    417,
    418,
    419,
    420,
    421,
    422,
    423,
    424,
    425,
    426,
    427,
    428,
    429,
    430,
    431,
    432,
    433,
    434,
    435,
    436,
    437,
    438,
    439,
    440,
    441,
    442,
    443,
    444,
    445,
    446,
    447,
    448,
    449,
    450,
    451,
    452,
    453,
    454,
    455,
    456,
    457,
    458,
};

uint16_t DDRSS_phyIndepRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    182,
    183,
    184,
    185,
    186,
    187,
    188,
    189,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
};

uint16_t DDRSS_phyRegNum[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    89,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    182,
    183,
    184,
    185,
    186,
    187,
    188,
    189,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    329,
    330,
    331,
    332,
    333,
    334,
    335,
    336,
    337,
    338,
    339,
    340,
    341,
    342,
    343,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    354,
    355,
    356,
    357,
    358,
    359,
    360,
    361,
    362,
    363,
    364,
    365,
    366,
    367,
    368,
    369,
    370,
    371,
    372,
    373,
    374,
    375,
    376,
    377,
    378,
    379,
    380,
    381,
    382,
    383,
    384,
    385,
    386,
    387,
    388,
    389,
    390,
    391,
    392,
    393,
    394,
    395,
    396,
    397,
    398,
    399,
    400,
    401,
    402,
    403,
    404,
    405,
    406,
    407,
    408,
    409,
    410,
    411,
    412,
    413,
    414,
    415,
    416,
    417,
    418,
    419,
    420,
    421,
    422,
    423,
    424,
    425,
    426,
    427,
    428,
    429,
    430,
    431,
    432,
    433,
    434,
    435,
    436,
    437,
    438,
    439,
    440,
    441,
    442,
    443,
    444,
    445,
    446,
    447,
    448,
    449,
    450,
    451,
    452,
    453,
    454,
    455,
    456,
    457,
    458,
    459,
    460,
    461,
    462,
    463,
    464,
    465,
    466,
    467,
    468,
    469,
    470,
    471,
    472,
    473,
    474,
    475,
    476,
    477,
    478,
    479,
    480,
    481,
    482,
    483,
    484,
    485,
    486,
    487,
    488,
    489,
    490,
    491,
    492,
    493,
    494,
    495,
    496,
    497,
    498,
    499,
    500,
    501,
    502,
    503,
    504,
    505,
    506,
    507,
    508,
    509,
    510,
    511,
    512,
    513,
    514,
    515,
    516,
    517,
    518,
    519,
    520,
    521,
    522,
    523,
    524,
    525,
    526,
    527,
    528,
    529,
    530,
    531,
    532,
    533,
    534,
    535,
    536,
    537,
    538,
    539,
    540,
    541,
    542,
    543,
    544,
    545,
    546,
    547,
    548,
    549,
    550,
    551,
    552,
    553,
    554,
    555,
    556,
    557,
    558,
    559,
    560,
    561,
    562,
    563,
    564,
    565,
    566,
    567,
    568,
    569,
    570,
    571,
    572,
    573,
    574,
    575,
    576,
    577,
    578,
    579,
    580,
    581,
    582,
    583,
    584,
    585,
    586,
    587,
    588,
    589,
    590,
    591,
    592,
    593,
    594,
    595,
    596,
    597,
    598,
    599,
    600,
    601,
    602,
    603,
    604,
    605,
    606,
    607,
    608,
    609,
    610,
    611,
    612,
    613,
    614,
    615,
    616,
    617,
    618,
    619,
    620,
    621,
    622,
    623,
    624,
    625,
    626,
    627,
    628,
    629,
    630,
    631,
    632,
    633,
    634,
    635,
    636,
    637,
    638,
    639,
    640,
    641,
    642,
    643,
    644,
    645,
    646,
    647,
    648,
    649,
    650,
    651,
    652,
    653,
    654,
    655,
    656,
    657,
    658,
    659,
    660,
    661,
    662,
    663,
    664,
    665,
    666,
    667,
    668,
    669,
    670,
    671,
    672,
    673,
    674,
    675,
    676,
    677,
    678,
    679,
    680,
    681,
    682,
    683,
    684,
    685,
    686,
    687,
    688,
    689,
    690,
    691,
    692,
    693,
    694,
    695,
    696,
    697,
    698,
    699,
    700,
    701,
    702,
    703,
    704,
    705,
    706,
    707,
    708,
    709,
    710,
    711,
    712,
    713,
    714,
    715,
    716,
    717,
    718,
    719,
    720,
    721,
    722,
    723,
    724,
    725,
    726,
    727,
    728,
    729,
    730,
    731,
    732,
    733,
    734,
    735,
    736,
    737,
    738,
    739,
    740,
    741,
    742,
    743,
    744,
    745,
    746,
    747,
    748,
    749,
    750,
    751,
    752,
    753,
    754,
    755,
    756,
    757,
    758,
    759,
    760,
    761,
    762,
    763,
    764,
    765,
    766,
    767,
    768,
    769,
    770,
    771,
    772,
    773,
    774,
    775,
    776,
    777,
    778,
    779,
    780,
    781,
    782,
    783,
    784,
    785,
    786,
    787,
    788,
    789,
    790,
    791,
    792,
    793,
    794,
    795,
    796,
    797,
    798,
    799,
    800,
    801,
    802,
    803,
    804,
    805,
    806,
    807,
    808,
    809,
    810,
    811,
    812,
    813,
    814,
    815,
    816,
    817,
    818,
    819,
    820,
    821,
    822,
    823,
    824,
    825,
    826,
    827,
    828,
    829,
    830,
    831,
    832,
    833,
    834,
    835,
    836,
    837,
    838,
    839,
    840,
    841,
    842,
    843,
    844,
    845,
    846,
    847,
    848,
    849,
    850,
    851,
    852,
    853,
    854,
    855,
    856,
    857,
    858,
    859,
    860,
    861,
    862,
    863,
    864,
    865,
    866,
    867,
    868,
    869,
    870,
    871,
    872,
    873,
    874,
    875,
    876,
    877,
    878,
    879,
    880,
    881,
    882,
    883,
    884,
    885,
    886,
    887,
    888,
    889,
    890,
    891,
    892,
    893,
    894,
    895,
    896,
    897,
    898,
    899,
    900,
    901,
    902,
    903,
    904,
    905,
    906,
    907,
    908,
    909,
    910,
    911,
    912,
    913,
    914,
    915,
    916,
    917,
    918,
    919,
    920,
    921,
    922,
    923,
    924,
    925,
    926,
    927,
    928,
    929,
    930,
    931,
    932,
    933,
    934,
    935,
    936,
    937,
    938,
    939,
    940,
    941,
    942,
    943,
    944,
    945,
    946,
    947,
    948,
    949,
    950,
    951,
    952,
    953,
    954,
    955,
    956,
    957,
    958,
    959,
    960,
    961,
    962,
    963,
    964,
    965,
    966,
    967,
    968,
    969,
    970,
    971,
    972,
    973,
    974,
    975,
    976,
    977,
    978,
    979,
    980,
    981,
    982,
    983,
    984,
    985,
    986,
    987,
    988,
    989,
    990,
    991,
    992,
    993,
    994,
    995,
    996,
    997,
    998,
    999,
    1000,
    1001,
    1002,
    1003,
    1004,
    1005,
    1006,
    1007,
    1008,
    1009,
    1010,
    1011,
    1012,
    1013,
    1014,
    1015,
    1016,
    1017,
    1018,
    1019,
    1020,
    1021,
    1022,
    1023,
    1024,
    1025,
    1026,
    1027,
    1028,
    1029,
    1030,
    1031,
    1032,
    1033,
    1034,
    1035,
    1036,
    1037,
    1038,
    1039,
    1040,
    1041,
    1042,
    1043,
    1044,
    1045,
    1046,
    1047,
    1048,
    1049,
    1050,
    1051,
    1052,
    1053,
    1054,
    1055,
    1056,
    1057,
    1058,
    1059,
    1060,
    1061,
    1062,
    1063,
    1064,
    1065,
    1066,
    1067,
    1068,
    1069,
    1070,
    1071,
    1072,
    1073,
    1074,
    1075,
    1076,
    1077,
    1078,
    1079,
    1080,
    1081,
    1082,
    1083,
    1084,
    1085,
    1086,
    1087,
    1088,
    1089,
    1090,
    1091,
    1092,
    1093,
    1094,
    1095,
    1096,
    1097,
    1098,
    1099,
    1100,
    1101,
    1102,
    1103,
    1104,
    1105,
    1106,
    1107,
    1108,
    1109,
    1110,
    1111,
    1112,
    1113,
    1114,
    1115,
    1116,
    1117,
    1118,
    1119,
    1120,
    1121,
    1122,
    1123,
    1124,
    1125,
    1126,
    1127,
    1128,
    1129,
    1130,
    1131,
    1132,
    1133,
    1134,
    1135,
    1136,
    1137,
    1138,
    1139,
    1140,
    1141,
    1142,
    1143,
    1144,
    1145,
    1146,
    1147,
    1148,
    1149,
    1150,
    1151,
    1152,
    1153,
    1154,
    1155,
    1156,
    1157,
    1158,
    1159,
    1160,
    1161,
    1162,
    1163,
    1164,
    1165,
    1166,
    1167,
    1168,
    1169,
    1170,
    1171,
    1172,
    1173,
    1174,
    1175,
    1176,
    1177,
    1178,
    1179,
    1180,
    1181,
    1182,
    1183,
    1184,
    1185,
    1186,
    1187,
    1188,
    1189,
    1190,
    1191,
    1192,
    1193,
    1194,
    1195,
    1196,
    1197,
    1198,
    1199,
    1200,
    1201,
    1202,
    1203,
    1204,
    1205,
    1206,
    1207,
    1208,
    1209,
    1210,
    1211,
    1212,
    1213,
    1214,
    1215,
    1216,
    1217,
    1218,
    1219,
    1220,
    1221,
    1222,
    1223,
    1224,
    1225,
    1226,
    1227,
    1228,
    1229,
    1230,
    1231,
    1232,
    1233,
    1234,
    1235,
    1236,
    1237,
    1238,
    1239,
    1240,
    1241,
    1242,
    1243,
    1244,
    1245,
    1246,
    1247,
    1248,
    1249,
    1250,
    1251,
    1252,
    1253,
    1254,
    1255,
    1256,
    1257,
    1258,
    1259,
    1260,
    1261,
    1262,
    1263,
    1264,
    1265,
    1266,
    1267,
    1268,
    1269,
    1270,
    1271,
    1272,
    1273,
    1274,
    1275,
    1276,
    1277,
    1278,
    1279,
    1280,
    1281,
    1282,
    1283,
    1284,
    1285,
    1286,
    1287,
    1288,
    1289,
    1290,
    1291,
    1292,
    1293,
    1294,
    1295,
    1296,
    1297,
    1298,
    1299,
    1300,
    1301,
    1302,
    1303,
    1304,
    1305,
    1306,
    1307,
    1308,
    1309,
    1310,
    1311,
    1312,
    1313,
    1314,
    1315,
    1316,
    1317,
    1318,
    1319,
    1320,
    1321,
    1322,
    1323,
    1324,
    1325,
    1326,
    1327,
    1328,
    1329,
    1330,
    1331,
    1332,
    1333,
    1334,
    1335,
    1336,
    1337,
    1338,
    1339,
    1340,
    1341,
    1342,
    1343,
    1344,
    1345,
    1346,
    1347,
    1348,
    1349,
    1350,
    1351,
    1352,
    1353,
    1354,
    1355,
    1356,
    1357,
    1358,
    1359,
    1360,
    1361,
    1362,
    1363,
    1364,
    1365,
    1366,
    1367,
    1368,
    1369,
    1370,
    1371,
    1372,
    1373,
    1374,
    1375,
    1376,
    1377,
    1378,
    1379,
    1380,
    1381,
    1382,
    1383,
    1384,
    1385,
    1386,
    1387,
    1388,
    1389,
    1390,
    1391,
    1392,
    1393,
    1394,
    1395,
    1396,
    1397,
    1398,
    1399,
    1400,
    1401,
    1402,
    1403,
    1404,
    1405,
    1406,
    1407,
    1408,
    1409,
    1410,
    1411,
    1412,
    1413,
    1414,
    1415,
    1416,
    1417,
    1418,
    1419,
    1420,
    1421,
    1422,
};
