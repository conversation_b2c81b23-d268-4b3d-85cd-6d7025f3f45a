I want to modify the IPC system to run Ipc_echo_test() on x86-64 architecture instead of the original J784S4 SoC. Since x86-64 doesn't have the same hardware mailbox and shared DDR as the J784S4, I need to:

1. Replace the hardware-specific VRING implementation with a Linux shared memory approach
2. Utilize the existing ivshmem-client (in ipc_test/examples/ivshmem_client) and ivshmem-server (in ipc_test/examples/ivshmem_server) code to:
   - Create a virtual mailbox mechanism for inter-core notifications
   - Set up shared memory regions that will contain the VirtIO rings
   - Implement the necessary interrupt mechanisms between cores

Specifically, I need help adapting the Ipc_initVirtIO() function and related code to use ivshmem for both the shared memory allocation and the mailbox notification system, while maintaining compatibility with the existing RPMessage and VirtIO APIs used by the echo test.

Use case explaination

Use case 0: Initialization
1. Initialize ivshmem-server. Source: ipc_test/examples/ivshmem/ivshmem_server
1.1 Start ivshmem-server process.
1.2 Server initializes a shared memory on Host for exchanging VRING data between processes. (Based on ipc_test/docs/VRING_MemoryLayout.txt)
2. Initialize ivshmem-client.
2.1 Start Core0 process.
2.2 Application call TI Driver API to initialize virtio.
2.3 IPC Driver initialize ivshmem-client. Use code from ipc_test/examples/ivshmem/ivshmem_client 
2.4 ivshmem-client connect to server.
2.4.1 The server need to realize client (by Core definitions).
2.4.2 Server responds to the client with the information: all other client fd (for doorbell function), VRING shared memory fd (to access shared VRING data)
2.4.3 Server notify all client with new client information.
2.4.4 Client store all information then notify server with confirmation.

Use case 1: Send message
1. A application sends a message to a given destination (CPU, endpoint).
1.1 Application call RPMessage_send() API
2. The message is first copied from the application to VRING used between the two CPUs.
2.1 IPC Driver request ivshmem-client write message to VRING shared memory.
3. After this the IPC driver posts the VRING ID in the HW mailbox.
3.1 IPC Driver request ivshmem-client to kick (notify) other client with VRING ID
3.2 ivshmem-client notify corresponding client by file descriptor
4. This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING
4.1 ivshmem-client receives notification. It gets VRING ID then checks for any messages in that VRING
5. If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint
5.1 ivshmem-client reads message from VRING then puts it in the destination RPMSG endpoint queue
5.2 It then triggers the application blocked on this RPMSG endpoint

Rules:
- Do not modify original source at packages folder
- Create new source files at ipc_test folder
- You can add new functions, files, makefiles, etc. as needed
- Leverage the existing ivshmem-client and ivshmem-server code as much as possible
- Focus on adapting Ipc_initVirtIO() and related functions to use ivshmem for shared memory and mailbox
- Ensure compatibility with the existing RPMessage and VirtIO APIs used by the echo test


# IPC System Adaptation for x86-64 Architecture

## Objective
Adapt the IPC system to run `Ipc_echo_test()` on x86-64 architecture instead of the original J784S4 SoC by implementing a Linux shared memory approach using ivshmem.

## Technical Background
The J784S4 SoC uses hardware mailboxes and shared DDR for inter-processor communication. Since x86-64 lacks these hardware features, we need to create software equivalents using Linux shared memory mechanisms.

## Implementation Requirements

### Core Components to Modify
1. Replace the hardware-specific VRING implementation with a Linux shared memory approach
2. Adapt `Ipc_initVirtIO()` function and related code to use ivshmem for:
   - Shared memory allocation for VirtIO rings
   - Mailbox notification system between processes

### Detailed Use Cases

#### Use Case 0: Initialization
1. **ivshmem-server Initialization**
   - Start ivshmem-server process
   - Server initializes shared memory on Host for VRING data exchange (following memory layout in `ipc_test/docs/VRING_MemoryLayout.txt`)

2. **ivshmem-client Initialization**
   - Start Core0 process
   - Application calls TI Driver API to initialize VirtIO
   - IPC Driver initializes ivshmem-client using code from `ipc_test/examples/ivshmem/ivshmem_client`
   - Client-server connection process:
     - Server identifies client based on Core definitions (packages/ti/drv/ipc/soc/V4/ipc_soc.h)
     - Server provides client with:
       * File descriptors for all other clients (for doorbell functionality)
       * VRING shared memory file descriptor
     - Server notifies all existing clients about the new client
     - New client stores information and confirms to server

#### Use Case 1: Message Transmission
1. **Message Sending**
   - Application calls `RPMessage_send()` API
   - IPC Driver requests ivshmem-client to write message to VRING shared memory
   - IPC Driver requests ivshmem-client to notify target client with VRING ID
   - ivshmem-client notifies corresponding client using file descriptor

2. **Message Reception**
   - ivshmem-client receives notification with VRING ID
   - Client checks for messages in the specified VRING
   - Client extracts message from VRING and places it in destination RPMSG endpoint queue
   - Application blocked on this RPMSG endpoint is triggered

### Implementation Rules
1. **Code Organization**:
   - DO NOT modify original source code in the `packages` folder
   - Create new source files in the `ipc_test` folder
   - Add new functions, files, makefiles as needed

2. **Code Reuse**:
   - Leverage existing ivshmem-client and ivshmem-server code wherever possible
   - Focus adaptation efforts on `Ipc_initVirtIO()` and directly related functions

3. **API Compatibility**:
   - Maintain full compatibility with existing RPMessage and VirtIO APIs
   - Ensure the echo test can run without modifications to its application code

4. **Required Deliverables**:
   - Modified or new implementation of `Ipc_initVirtIO()`
   - Any supporting functions needed for the ivshmem-based approach
   - Build system modifications if needed

### Implementation Approach
1. **ipc_test/examples/ivshmem/ivshmem_server**
   - Start ivshmem-server process
   - Initialize shared memory on Host for VRING data exchange
   - Handle client connections:
        - Identify client based on Core definitions
        - Provide client with VRING shared memory file descriptor
        - Notify all existing clients about the new client
        - Handle client disconnections and notifications
2. **ipc_test/stub/drv/ipc/src/ipc_virtio.c**
   - Modify `Ipc_initVirtIO()`
        - Initialize ivshmem-client. ivshmem_client connect ivshmem-server to get VRING shared memory file descriptor. ivshmem_client store other client information.
        - Update vringBaseAddr to use VRING shared memory file descriptor
3. **packages/ti/drv/ipc/src/ipc_mailbox.c**
   - Modify `Ipc_mailboxSend()`
        - Request ivshmem-client to notify target client with VRING ID
   - Modify `Mailbox_plugInterrupt()`
        - Request ivshmem-client to register interrupt callback
   - Modify `Ipc_mailboxClear()`
        - Request ivshmem-client to clear interrupt
4. Update CMakeList.txt (in ipc_test folder) to build ivshmem-server (ipc_test/examples/ivshmem/ivshmem_server) and ipc_test_client (ipc_test/examples/common/src)

Utilize the existing code in:
- `ipc_test/examples/ivshmem/ivshmem_client`
- `ipc_test/examples/ivshmem/ivshmem_server`
- Current source code ipc_test already support built and run on x86-64. No need to modify.
