/*
 *  \file   ivshmem_common.h
 *
 *  \brief  Common header file for ivshmem
 *
 */

#ifndef IVSHMEM_COMMON_H
#define IVSHMEM_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>

#include <ti/csl/cslr_navss_main.h>
#include <ti/drv/ipc/soc/V4/ipc_soc.h>
#include <ti/drv/ipc/src/ipc_mailbox.h>
#include <ti/drv/ipc/include/ipc_config.h>
#include "ipc_setup.h"

/* log a message on stdout if verbose=1 */
#define IVSHMEM_CLIENT_DEBUG(client, fmt, ...)                                 \
  do {                                                                         \
    printf(fmt, ##__VA_ARGS__);                                                \
  } while (0)

/* log a message on stdout if verbose=1 */
#define IVSHMEM_SERVER_DEBUG(server, fmt, ...)                                 \
  do {                                                                         \
    printf(fmt, ##__VA_ARGS__);                                                \
  } while (0)

/**
 * Default number of notification vectors supported by the client/server
 */
#define IVSHMEM_DEFAULT_VECTORS   (1U)

/**
 * Maximum number of notification vectors supported by the client/server
 */
#define IVSHMEM_MAX_VECTORS (IPC_MAX_PROCS + 1U)

/**
 * Maximum number of notification vectors supported by the client
 */
#define IVSHMEM_CLIENT_MAX_VECTORS (IPC_MAX_PROCS)

/**
 * Maximum number of notification vectors supported by the server
 */
#define IVSHMEM_SERVER_MAX_VECTORS (64U)

/**
 * ivshmem protocol version
 */
#define IVSHMEM_PROTOCOL_VERSION (0U)

/**
 * ivshmem default unix sock path
 */
#define IVSHMEM_DEFAULT_UDS_PATH "/tmp/tda4-ivshmem.sock"

/**
 * ivshmem client default verbose
 */
#define IVSHMEM_CLIENT_DEFAULT_VERBOSE (1U)

/**
 * ivshmem mailbox base address
 */
#define MAILBOX_BASE_ADDRESS (CSL_NAVSS_MAIN_MAILBOX_REGS_0_BASE)

/**
 * ivshmem ipc mailbox shared memory size
 */
#define IPC_MAILBOX_SHM_SIZE                                                   \
  (CSL_NAVSS_MAIN_MAILBOX_REGS_18_BASE - CSL_NAVSS_MAIN_MAILBOX_REGS_0_BASE)

/**
 * ivshmem vring default shared memory path
 */
#define IVSHMEM_VRING_DEFAULT_SHM_PATH "vring_ivshmem"

/**
 * ivshmem mailbox default shared memory path
 */
#define IVSHMEM_MAILBOX_DEFAULT_SHM_PATH "mailbox_ivshmem"

typedef struct {
  uint32_t daTxOffset; /* Buffer offset address for TX to remote core */
  uint32_t daRxOffset; /* Buffer offset address for RX from remote core */
  uint32_t txNotifyId; /* Notify Id for TX */
  uint32_t rxNotifyId; /* Notify Id for RX */
} Vring_Info;

typedef struct {
  uint32_t clusterId; /* Cluster id for mailbox */
  uint32_t userId;    /* User id for mailbox */
  uint32_t queueId;   /* Queue id for mailbox */
  uint32_t baseAddr;      /* Base address for mailbox */
  uint32_t msgOffset;      /* Offset for mailbox message */
  uint32_t irqStatusClrOffset;      /* Offset for mailbox irq status clear */
  uint32_t irqEnableSetOffset;      /* Offset for mailbox irq enable set */
  uint32_t irqEnableClrOffset;      /* Offset for mailbox irq enable clear */
} Mailbox_Info;

static inline void dump_Mailbox_Info(Mailbox_Info *mailbox_info) {
  printf("Mailbox_Info: clusterId=%u, userId=%u, queueId=%u, baseAddr=0x%08x, "
         "msgOffset=0x%08x, irqStatusClrOffset=0x%08x, irqEnableSetOffset=0x%08x, "
         "irqEnableClrOffset=0x%08x\n",
         mailbox_info->clusterId, mailbox_info->userId, mailbox_info->queueId,
         mailbox_info->baseAddr, mailbox_info->msgOffset,
         mailbox_info->irqStatusClrOffset, mailbox_info->irqEnableSetOffset,
         mailbox_info->irqEnableClrOffset);
}

static inline int64_t core2peer_converter(uint32_t core_id) {
  return core_id >= UINT32_MAX ? -1 : core_id + 1;
}

static inline uint32_t peer2core_converter(int64_t peer_id) {
  return ((peer_id < 1) || (peer_id > UINT32_MAX)) ? 0U : (peer_id - 1U);
}

#ifdef __cplusplus
}
#endif

#endif /* IVSHMEM_COMMON_H */
