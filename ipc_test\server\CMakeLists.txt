cmake_minimum_required(VERSION 3.10)
project(ivshmem_server CXX)

set(IVSHMEM_SERVER_SRCS
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ivshmem-server.c
    ${CMAKE_CURRENT_SOURCE_DIR}/src/main-test.c
    # ${QEMU_DIR}/util/event_notifier-posix.c
    ${QEMU_DIR}/util/event_notifier-win32.c
    # ${QEMU_DIR}/util/oslib-posix.c
    ${QEMU_DIR}/util/oslib-win32.c
    ${QEMU_DIR}/util/osdep.c
)

# Loop through each file and set its language to CXX
foreach(src_file ${IVSHMEM_SERVER_SRCS})
    set_property(SOURCE ${src_file} PROPERTY LANGUAGE CXX)
endforeach()

# Define the ivshmem_server executable
add_executable(ivshmem_server
    ${IVSHMEM_SERVER_SRCS}
)

# Set compile definitions
target_compile_definitions(ivshmem_server PRIVATE
    SOC_J784S4
)

# Set include directories
target_include_directories(ivshmem_server PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/inc
    ${COM_DIR}
    ${CMAKE_SOURCE_DIR}/client/app/inc
    ${TI_PACKAGE_DIR}
    ${QEMU_DIR}/include
    /usr/include/glib-2.0
    /usr/lib/x86_64-linux-gnu/glib-2.0/include
    /usr/lib/glib-2.0/include
    /usr/include
)

# Link libraries
target_link_libraries(ivshmem_server
    pthread
    rt
    glib-2.0
)

install(TARGETS ivshmem_server DESTINATION bin)
