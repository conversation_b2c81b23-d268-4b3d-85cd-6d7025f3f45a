
%%{
/*!
 *  This template implements the Settings.xdc
 */  
  /* Versioning */
  var ver = this;
  for each(i=0;i<ver.length;i++)
  {
      if(String(ver[i]).length < 2)
      {
        ver[i]="0"+ver[i];
      }
  }
  
  var packageVersion = "\""+ver[0]+"."+ver[1]+"."+ver[2]+"."+ver[3]+"\"";

%%}

module Settings
{
    config string boardVersionString = `packageVersion`;
    config string boardName = "";
	
    /*! This variable is to control the device library type selection.
     * By default this variable is set to release.
     * 
     * To use CSL to use the debug/release library, add the following lines to config
     * file and set the library profile accordingly:
     * 
     *      var Uart Settings = xdc.useModule ('ti.Uart.Settings');
     *      UartSettings.libProfile = "debug";
     * 
     */
    metaonly config string libProfile = "release";	
    
}


