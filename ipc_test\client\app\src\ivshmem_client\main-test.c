#include <errno.h>
#include <fcntl.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <unistd.h>

// Include the ivshmem-client header
#include "ivshmem-client.h"

#define SHMEM_SIZE (1 * 1024 * 1024) // 1MB
#define IVSHMEM_CLIENT_DEFAULT_VERBOSE (1U)
#define IVSHMEM_CLIENT_DEFAULT_UNIX_SOCK_PATH "/tmp/ivshmem.sock"

// Shared memory pointer
void *shmem = NULL;

typedef struct IvshmemClientArgs {
  bool verbose;
  const char *unix_sock_path;
  int64_t peer_id;
} IvshmemClientArgs;

/* show ivshmem_client_usage and exit with given error code */
static void ivshmem_client_usage(const char *name, int code) {
  fprintf(stderr, "%s [opts]\n", name);
  fprintf(stderr, "  -h: show this help\n");
  fprintf(stderr,
          "  -v: verbose mode\n"
          "     default=%s\n",
          ((IVSHMEM_CLIENT_DEFAULT_VERBOSE == 1U) ? "yes" : "no"));
  fprintf(stderr,
          "  -S <unix_sock_path>: path to the unix socket\n"
          "     to connect to.\n"
          "     default=%s\n",
          IVSHMEM_CLIENT_DEFAULT_UNIX_SOCK_PATH);
  fprintf(stderr, "  -c <core_id>: TI IPC core ID to request from the server\n"
                  "     default=1 (IPC_MPU1_0)\n");
  exit(code);
}

/* parse the program arguments, exit on error */
static void ivshmem_client_parse_args(IvshmemClientArgs *args, int argc,
                                      char *argv[]) {
  int c;

  while ((c = getopt(argc, argv,
                     "h"  /* help */
                     "v"  /* verbose */
                     "S:" /* unix_sock_path */
                     "c:" /* peer_id */
                     )) != -1) {

    switch (c) {
    case 'h': /* help */
      ivshmem_client_usage(argv[0], 0);
      break;

    case 'v': /* verbose */
      args->verbose = 1;
      break;

    case 'S': /* unix_sock_path */
      args->unix_sock_path = optarg;
      break;

    case 'c': /* peer_id */
      args->peer_id = atoi(optarg);
      break;

    default:
      ivshmem_client_usage(argv[0], 1);
      break;
    }
  }
}

/* show command line help */
static void ivshmem_client_cmdline_help(void) {
  printf("dump: dump peers (including us)\n"
         "int <peer> <vector>: notify one vector on a peer\n"
         "int <peer> all: notify all vectors of a peer\n"
         "int all: notify all vectors of all peers (excepting us)\n"
         "send <peer> <msg>: write message to shared memory then notify vector "
         "0 on a peer\n");
}

/* read stdin and handle commands */
static int ivshmem_client_handle_stdin_command(IvshmemClient *client) {
  IvshmemClientPeer *peer;
  char buf[128];
  char *s, *token;
  int ret;
  int peer_id, vector;
  uint64_t msg;

  memset(buf, 0, sizeof(buf));
  ret = read(0, buf, sizeof(buf) - 1);
  if (ret < 0) {
    return -1;
  }

  s = buf;
  while ((token = strsep(&s, "\n\r;")) != NULL) {
    if (!strcmp(token, "")) {
      continue;
    }
    if (!strcmp(token, "?")) {
      ivshmem_client_cmdline_help();
    }
    if (!strcmp(token, "help")) {
      ivshmem_client_cmdline_help();
    } else if (!strcmp(token, "dump")) {
      ivshmem_client_dump(client);
    } else if (!strcmp(token, "int all")) {
      ivshmem_client_notify_broadcast(client);
    } else if (sscanf(token, "int %d %d", &peer_id, &vector) == 2) {
      peer = ivshmem_client_search_peer(client, peer_id);
      if (peer == NULL) {
        printf("cannot find peer_id = %d\n", peer_id);
        continue;
      }
      ivshmem_client_notify(client, peer, vector);
    } else if (sscanf(token, "int %d all", &peer_id) == 1) {
      peer = ivshmem_client_search_peer(client, peer_id);
      if (peer == NULL) {
        printf("cannot find peer_id = %d\n", peer_id);
        continue;
      }
      ivshmem_client_notify_all_vects(client, peer);
    } else if (sscanf(token, "send %d %lu", &peer_id, &msg) == 2) {
      int wr_res;
      Vring_Info vring_info;
      unsigned vector = 0; /* Default send to vector 0 */

      peer = ivshmem_client_search_peer(client, peer_id);
      if (peer == NULL) {
        printf("cannot find peer_id = %d\n", peer_id);
        continue;
      }

      uint32_t self_id = peer2core_converter(client->local.id);
      uint32_t remote_id = peer2core_converter(peer->id);

      ivshmem_client_get_vring_info(self_id, remote_id, &vring_info);
      if (wr_res =
              ivshmem_client_write_msg(VRING_BASE_ADDRESS, &msg, sizeof(msg),
                                       vring_info.daTxOffset) != 0) {
        printf("cannot write msg. error %d\n", wr_res);
        continue;
      }
      ivshmem_client_notify_value(client, peer, vector, client->local.id);
    } else {
      printf("invalid command, type help\n");
    }
  }

  printf("cmd> ");
  fflush(stdout);
  return 0;
}

/* listen on stdin (command line), on unix socket (notifications of new
 * and dead peers), and on eventfd (IRQ request) */
static int ivshmem_client_poll_events(IvshmemClient *client) {
  fd_set fds;
  int ret, maxfd;

  while (1) {

    FD_ZERO(&fds);
    FD_SET(0, &fds); /* add stdin in fd_set */
    maxfd = 1;

    ivshmem_client_get_fds(client, &fds, &maxfd);

    ret = select(maxfd, &fds, NULL, NULL, NULL);
    if (ret < 0) {
      if (errno == EINTR) {
        continue;
      }

      fprintf(stderr, "select error: %s\n", strerror(errno));
      break;
    }
    if (ret == 0) {
      continue;
    }

    if (FD_ISSET(0, &fds) && ivshmem_client_handle_stdin_command(client) < 0 &&
        errno != EINTR) {
      fprintf(stderr, "ivshmem_client_handle_stdin_command() failed\n");
      break;
    }

    if (ivshmem_client_handle_fds(client, &fds, maxfd) < 0) {
      fprintf(stderr, "ivshmem_client_handle_fds() failed\n");
      break;
    }
  }

  return ret;
}

/* callback when we receive a notification (just display it) */
static void ivshmem_client_notification_cb(const IvshmemClient *client,
                                           const IvshmemClientPeer *peer,
                                           unsigned vect, void *arg) {
  (void)vect;
  (void)arg;
  uint32_t vring_msg;
  uint32_t mailbox_msg;
  Vring_Info vring_info;
  Mailbox_Info mailbox_info;

  uint32_t self_id = peer2core_converter(client->local.id);
  uint32_t remote_id = peer2core_converter(peer->id);

  ivshmem_client_get_mailbox_info(self_id, remote_id, &mailbox_info);
  ivshmem_client_read_msg(mailbox_info.baseAddr, &mailbox_msg, sizeof(mailbox_msg), mailbox_info.msgOffset);

  ivshmem_client_get_vring_info(self_id, remote_id, &vring_info);
  ivshmem_client_read_msg(VRING_BASE_ADDRESS, &vring_msg,
                          sizeof(vring_msg), vring_info.daRxOffset);

  printf("receive notification from peer_id=%" PRIu64
         " remote_id=%u msg_mailbox=0x%08x msg_vring=%u\n",
         peer->id, remote_id, mailbox_msg, vring_msg);
}

int main(int argc, char *argv[]) {
  struct sigaction sa;
  IvshmemClient client;
  IvshmemClientArgs args = {
      .verbose = IVSHMEM_CLIENT_DEFAULT_VERBOSE,
      .unix_sock_path = IVSHMEM_CLIENT_DEFAULT_UNIX_SOCK_PATH,
      .peer_id = IPC_MPU1_0,
  };

  /* parse arguments, will exit on error */
  ivshmem_client_parse_args(&args, argc, argv);

  /* Ignore SIGPIPE, see this link for more info:
   * http://www.mail-archive.com/<EMAIL>/msg01606.html */
  sa.sa_handler = SIG_IGN;
  sa.sa_flags = 0;
  if (sigemptyset(&sa.sa_mask) == -1 || sigaction(SIGPIPE, &sa, 0) == -1) {
    perror("failed to ignore SIGPIPE; sigaction");
    return 1;
  }

  ivshmem_client_cmdline_help();
  printf("cmd> ");
  fflush(stdout);

  if (ivshmem_client_init(&client, args.unix_sock_path,
                          ivshmem_client_notification_cb, NULL, args.verbose,
                          args.peer_id) < 0) {
    fprintf(stderr, "cannot init client\n");
    return 1;
  }

  while (1) {
    if (ivshmem_client_connect(&client) < 0) {
      fprintf(stderr, "cannot connect to server, retry in 1 second\n");
      sleep(1);
      continue;
    }

    fprintf(stdout, "listen on server socket %d\n", client.sock_fd);

    if (ivshmem_client_poll_events(&client) == 0) {
      continue;
    }

    /* disconnected from server, reset all peers */
    fprintf(stdout, "disconnected from server\n");

    ivshmem_client_close(&client);
  }

  return 0;
}
