/**
 * Note: This file was auto-generated by TI PinMux on 15/10/2024.
 *
 * \file   J784S4_pinmux.h
 *
 * \brief  This file contains pad configure register offsets and bit-field 
 *         value macros for different configurations,
 *
 *           BIT[21]		TXDISABLE		disable the pin's output driver
 *           BIT[18]		RXACTIVE		enable the pin's input buffer (typically kept enabled)
 *           BIT[17]		PULLTYPESEL		set the iternal resistor pull direction high or low (if enabled)
 *           BIT[16]		PULLUDEN		internal resistor disable (0 = enabled / 1 = disabled)
 *           BIT[3:0]		MUXMODE			select the desired function on the given pin
 *
 *  \copyright Copyright (CU) 2024 Texas Instruments Incorporated - 
 *             http://www.ti.com/
 */

#ifndef _J784S4_PIN_MUX_H_
#define _J784S4_PIN_MUX_H_

/* ========================================================================== */
/*                             Include Files                                  */
/* ========================================================================== */

#include <ti/board/src/j784s4_evm/include/pinmux.h>
#include <ti/csl/csl_types.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================== */
/*                           Macros & Typedefs                                */
/* ========================================================================== */
#define PIN_MODE(mode)	                (mode)
#define PINMUX_END                      (-1)

/** \brief Active mode configurations */
/** \brief Resistor enable */
#define PIN_PULL_DISABLE                (0x1U << 16U)
/** \brief Pull direction */
#define	PIN_PULL_DIRECTION              (0x1U << 17U)
/** \brief Receiver enable */
#define	PIN_INPUT_ENABLE                (0x1U << 18U)
/** \brief Driver disable */
#define	PIN_OUTPUT_DISABLE              (0x1U << 21U)
/** \brief Wakeup enable */
#define	PIN_WAKEUP_ENABLE               (0x1U << 29U)

/** \brief Pad config register offset in control module */

enum pinMainOffsets
{
	PIN_MCASP1_AXR0		 = 0x0C0,
	PIN_MCASP1_AFSX		 = 0x0BC,
	PIN_MCASP1_ACLKX		 = 0x0B8,
	PIN_MCASP0_AXR12		 = 0x0A0,
	PIN_MCASP0_AXR13		 = 0x0A4,
	PIN_MCASP0_AXR14		 = 0x0A8,
	PIN_MCASP1_AXR3		 = 0x0B0,
	PIN_MCASP0_AXR15		 = 0x0AC,
	PIN_MCASP0_AXR7		 = 0x08C,
	PIN_MCASP0_AXR8		 = 0x090,
	PIN_MCASP0_AXR9		 = 0x094,
	PIN_MCASP0_AXR10		 = 0x098,
	PIN_MCASP1_AXR4		 = 0x0B4,
	PIN_MCASP0_AXR11		 = 0x09C,
	PIN_MCASP2_AXR0		 = 0x05C,
	PIN_MCASP2_AFSX		 = 0x058,
	PIN_SPI0_CS0		 = 0x0CC,
	PIN_MCAN13_TX		 = 0x00C,
	PIN_MCAN15_RX		 = 0x020,
	PIN_GPIO0_11		 = 0x02C,
	PIN_MCASP0_AXR2		 = 0x048,
	PIN_MCASP2_ACLKX		 = 0x054,
	PIN_MCAN0_RX		 = 0x068,
	PIN_MCAN1_RX		 = 0x070,
	PIN_MCAN14_TX		 = 0x014,
	PIN_MCAN13_RX		 = 0x010,
	PIN_MCAN15_TX		 = 0x01C,
	PIN_MCAN14_RX		 = 0x018,
	PIN_MCAN0_TX		 = 0x064,
	PIN_MCASP2_AXR1		 = 0x060,
	PIN_ECAP0_IN_APWM_OUT		 = 0x0C4,
	PIN_EXT_REFCLK1		 = 0x0C8,
	PIN_I2C0_SCL		 = 0x0E0,
	PIN_I2C0_SDA		 = 0x0E4,
	PIN_TMS		 = 0x11C,
	PIN_MCAN16_RX		 = 0x028,
	PIN_MCAN16_TX		 = 0x024,
	PIN_MCASP0_AFSX		 = 0x03C,
	PIN_MCASP0_ACLKX		 = 0x038,
	PIN_MCASP0_AXR4		 = 0x080,
	PIN_MCASP0_AXR3		 = 0x07C,
	PIN_MCASP0_AXR6		 = 0x088,
	PIN_MCASP0_AXR5		 = 0x084,
	PIN_MMC1_CLK		 = 0x104,
	PIN_MMC1_CMD		 = 0x108,
	PIN_MMC1_CLKLB		 = 0x100,
	PIN_MMC1_DAT0		 = 0x0FC,
	PIN_MMC1_DAT1		 = 0x0F8,
	PIN_MMC1_DAT2		 = 0x0F4,
	PIN_MMC1_DAT3		 = 0x0F0,
	PIN_TIMER_IO0		 = 0x0E8,
	PIN_EXTINTN		 = 0x000,
	PIN_RESETSTATZ		 = 0x10C,
	PIN_SOC_SAFETY_ERRORN		 = 0x110,
	PIN_PMIC_WAKE0N		 = 0x034,
	PIN_MCAN12_RX		 = 0x008,
	PIN_MCAN12_TX		 = 0x004,
	PIN_MCASP1_AXR1		 = 0x04C,
	PIN_MCASP1_AXR2		 = 0x050,
	PIN_GPIO0_12		 = 0x030,
	PIN_MCAN1_TX		 = 0x06C,
	PIN_MCAN2_TX		 = 0x074,
	PIN_MCAN2_RX		 = 0x078,
	PIN_SPI0_D0		 = 0x0D8,
	PIN_SPI0_D1		 = 0x0DC,
	PIN_MCASP0_AXR0		 = 0x040,
	PIN_MCASP0_AXR1		 = 0x044,
	PIN_SPI0_CS1		 = 0x0D0,
	PIN_SPI0_CLK		 = 0x0D4,
	PIN_TIMER_IO1		 = 0x0EC,
};

enum pinWkupOffsets
{
	PIN_TDI		 = 0x114,
	PIN_TDO		 = 0x118,
	PIN_MCU_ADC0_AIN0		 = 0x134,
	PIN_MCU_ADC0_AIN1		 = 0x138,
	PIN_MCU_ADC0_AIN2		 = 0x13C,
	PIN_MCU_ADC0_AIN3		 = 0x140,
	PIN_MCU_ADC0_AIN4		 = 0x144,
	PIN_MCU_ADC0_AIN5		 = 0x148,
	PIN_MCU_ADC0_AIN6		 = 0x14C,
	PIN_MCU_ADC0_AIN7		 = 0x150,
	PIN_MCU_RGMII1_RD0		 = 0x094,
	PIN_MCU_RGMII1_RD1		 = 0x090,
	PIN_MCU_RGMII1_RD2		 = 0x08C,
	PIN_MCU_RGMII1_RD3		 = 0x088,
	PIN_MCU_RGMII1_RXC		 = 0x084,
	PIN_MCU_RGMII1_RX_CTL		 = 0x06C,
	PIN_MCU_RGMII1_TD0		 = 0x07C,
	PIN_MCU_RGMII1_TD1		 = 0x078,
	PIN_MCU_RGMII1_TD2		 = 0x074,
	PIN_MCU_RGMII1_TD3		 = 0x070,
	PIN_MCU_RGMII1_TXC		 = 0x080,
	PIN_MCU_RGMII1_TX_CTL		 = 0x068,
	PIN_MCU_I2C0_SCL		 = 0x108,
	PIN_MCU_I2C0_SDA		 = 0x10C,
	PIN_WKUP_GPIO0_8		 = 0x0E0,
	PIN_WKUP_GPIO0_9		 = 0x0E4,
	PIN_WKUP_GPIO0_11		 = 0x0EC,
	PIN_MCU_MCAN0_RX		 = 0x0BC,
	PIN_MCU_MCAN0_TX		 = 0x0B8,
	PIN_WKUP_GPIO0_5		 = 0x0D4,
	PIN_WKUP_GPIO0_4		 = 0x0D0,
	PIN_MCU_MDIO0_MDC		 = 0x09C,
	PIN_MCU_MDIO0_MDIO		 = 0x098,
	PIN_MCU_OSPI0_CLK		 = 0x000,
	PIN_MCU_OSPI0_CSN0		 = 0x02C,
	PIN_MCU_OSPI0_D0		 = 0x00C,
	PIN_MCU_OSPI0_D1		 = 0x010,
	PIN_MCU_OSPI0_D2		 = 0x014,
	PIN_MCU_OSPI0_D3		 = 0x018,
	PIN_MCU_OSPI0_D4		 = 0x01C,
	PIN_MCU_OSPI0_D5		 = 0x020,
	PIN_MCU_OSPI0_D6		 = 0x024,
	PIN_MCU_OSPI0_D7		 = 0x028,
	PIN_MCU_OSPI0_DQS		 = 0x008,
	PIN_MCU_OSPI0_CSN3		 = 0x03C,
	PIN_MCU_OSPI0_CSN2		 = 0x038,
	PIN_MCU_OSPI1_CLK		 = 0x040,
	PIN_MCU_OSPI1_CSN0		 = 0x05C,
	PIN_MCU_OSPI1_D0		 = 0x04C,
	PIN_MCU_OSPI1_D1		 = 0x050,
	PIN_MCU_OSPI1_D2		 = 0x054,
	PIN_MCU_OSPI1_D3		 = 0x058,
	PIN_MCU_OSPI1_DQS		 = 0x048,
	PIN_MCU_OSPI1_LBCLKO		 = 0x044,
	PIN_WKUP_GPIO0_14		 = 0x0F8,
	PIN_WKUP_GPIO0_15		 = 0x0FC,
	PIN_WKUP_GPIO0_13		 = 0x0F4,
	PIN_WKUP_GPIO0_12		 = 0x0F0,
	PIN_WKUP_GPIO0_0		 = 0x0C0,
	PIN_WKUP_GPIO0_1		 = 0x0C4,
	PIN_WKUP_GPIO0_2		 = 0x0C8,
	PIN_WKUP_GPIO0_3		 = 0x0CC,
	PIN_WKUP_GPIO0_6		 = 0x0D8,
	PIN_WKUP_GPIO0_7		 = 0x0DC,
	PIN_MCU_SPI0_CLK		 = 0x0A0,
	PIN_MCU_SPI0_D0		 = 0x0A4,
	PIN_WKUP_GPIO0_56		 = 0x120,
	PIN_WKUP_GPIO0_57		 = 0x17C,
	PIN_WKUP_GPIO0_66		 = 0x180,
	PIN_MCU_SPI0_D1		 = 0x0A8,
	PIN_MCU_SPI0_CS0		 = 0x0AC,
	PIN_MCU_OSPI0_CSN1		 = 0x030,
	PIN_MCU_OSPI1_CSN1		 = 0x060,
	PIN_WKUP_I2C0_SCL		 = 0x100,
	PIN_WKUP_I2C0_SDA		 = 0x104,
	PIN_EMU0		 = 0x12C,
	PIN_EMU1		 = 0x130,
	PIN_TCK		 = 0x124,
	PIN_TRSTN		 = 0x128,
	PIN_WKUP_GPIO0_67		 = 0x184,
	PIN_MCU_RESETSTATZ		 = 0x11C,
	PIN_MCU_RESETZ		 = 0x118,
	PIN_PMIC_POWER_EN1		 = 0x110,
	PIN_RESET_REQZ		 = 0x174,
	PIN_WKUP_GPIO0_49		 = 0x190,
	PIN_WKUP_GPIO0_10		 = 0x0E8,
	PIN_MCU_SAFETY_ERRORN		 = 0x114,
	PIN_PORZ		 = 0x178,
	PIN_WKUP_UART0_RXD		 = 0x0B0,
	PIN_WKUP_UART0_TXD		 = 0x0B4,
};

/* ========================================================================== */
/*                            Global Variables                                */
/* ========================================================================== */

/** \brief Pinmux configuration data for the board. Auto-generated from 
           Pinmux tool. */
extern pinmuxBoardCfg_t gJ784S4_MainPinmuxData[];
extern pinmuxBoardCfg_t gJ784S4_WkupPinmuxData[];

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* _J784S4_PIN_MUX_H_ */
