/*
 * Copyright (c) 2018-2022, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
/*
 *  ======== csl_cache.c ========
 */


#include <stdint.h>
#include <stdbool.h>
#include <stdlib.h>

#include <ti/csl/soc.h>
#include <ti/csl/arch/csl_arch.h>
#include "csl_arm_r5_priv.h"

void CSL_armR5CacheWb(const void * addr, uint32_t size, bool wait)
{
   uintptr_t i, firstAddr, lastAddr;
   uint32_t cacheLineSize = CSL_armR5CacheGetDcacheLineSize();

   /* Calculate the last address */
   lastAddr = (uintptr_t) addr + size;

   /* find the first address for cache Wb */
   firstAddr = (uintptr_t ) addr & ~(cacheLineSize -1U);
   
   /* Ensure all previous memory accesses complete */
   CSL_armR5Dmb();

   for (i = firstAddr; i < lastAddr; i+=cacheLineSize)
   {
      /* clean single entry in DCache to PoC */
       CSL_armR5CacheCleanDcacheMva(i);
   }
   
   if (wait)
   {
       /* drain write buffer */
       CSL_armR5Dsb();
   }
   
   return;
}
void CSL_armR5CacheWbInv(const void * addr, uint32_t size, bool wait)
{
    uintptr_t i, firstAddr, lastAddr;
    uint32_t cacheLineSize = CSL_armR5CacheGetDcacheLineSize();

    /* Calculate the last address */
    lastAddr = (uintptr_t) addr + size;

    /* find the first address for cache Wb */
    firstAddr = (uintptr_t ) addr & ~(cacheLineSize -1U);

   /* Ensure all previous memory accesses complete */
    CSL_armR5Dmb();

    for (i = firstAddr; i < lastAddr; i+=cacheLineSize)
    {
        /* clean and invalidate single entry in DCache to PoC */
        CSL_armR5CacheCleanInvalidateDcacheMva(i);
    }

    if (wait)
    {
       /* drain write buffer */
       CSL_armR5Dsb();
    }

    return;

}
void CSL_armR5CacheInv(const void * addr, uint32_t size, bool wait)
{
    uintptr_t i, firstAddr, lastAddr;
    uint32_t cacheLineSize = CSL_armR5CacheGetDcacheLineSize();

    /* Calculate the last address */
    lastAddr = (uintptr_t) addr + size;

    /* find the first address for cache Wb */
    firstAddr = (uintptr_t ) addr & ~(cacheLineSize -1U);

   /* Ensure all previous memory accesses complete */
    CSL_armR5Dmb();

    for (i = firstAddr; i < lastAddr; i+=cacheLineSize)
    {
        /* invalidate single entry in DCache */
        CSL_armR5CacheInvalidateDcacheMva(i);
    }

    if (wait)
    {
       /* drain write buffer */
       CSL_armR5Dsb();
       /* flush prefetch buffer */
       CSL_armR5Isb();
    }

    return;
}

void CSL_armR5CacheWait(void)
{
    /* drain write buffer */
    asm ( " DSB ");

    return;
}


/* Nothing past this point */
