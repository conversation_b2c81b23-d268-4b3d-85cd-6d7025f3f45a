// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (C) 2020 Texas Instruments Incorporated - https://www.ti.com/
 */

/dts-v1/;

#include "k3-j7200-som-p0.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/net/ti-dp83867.h>
#include <dt-bindings/mux/ti-serdes.h>
#include <dt-bindings/phy/phy.h>

/ {

	aliases {
		mmc0 = &main_sdhci0;  // eMMC
		mmc1 = &main_sdhci1;  // SD
	};

	chosen {
		stdout-path = "serial2:115200n8";
		bootargs = "console=ttyS2,115200n8 earlycon=ns16550a,mmio32,0x02800000 root=/dev/mmcblk1p2 rw rootfstype=ext4 rootwait";
	};

	cpsw5g_virt_mac: main_r5fss_cpsw5g_virt_mac0 {
		compatible = "ti,j721e-cpsw-virt-mac";
		dma-coherent;
		ti,psil-base = <0x4a00>;
		ti,remote-name = "mpu_1_0_ethswitch-device-0";

		dmas = <&main_udmap 0xca00>,
		       <&main_udmap 0xca01>,
		       <&main_udmap 0xca02>,
		       <&main_udmap 0xca03>,
		       <&main_udmap 0xca04>,
		       <&main_udmap 0xca05>,
		       <&main_udmap 0xca06>,
		       <&main_udmap 0xca07>,
		       <&main_udmap 0x4a00>;
		dma-names = "tx0", "tx1", "tx2", "tx3",
			    "tx4", "tx5", "tx6", "tx7",
			    "rx";

		virt_emac_port {
			ti,label = "virt-port";
			/* local-mac-address = [0 0 0 0 0 0]; */
		};
	};

	cpsw9g_virt_maconly: main-r5fss-cpsw9g-virt-mac1 {
		compatible = "ti,j721e-cpsw-virt-mac";
		dma-coherent;
		ti,psil-base = <0x4a00>;
		ti,remote-name = "mpu_1_0_ethmac-device-1";

		dmas = <&main_udmap 0xca00>,
		       <&main_udmap 0xca01>,
		       <&main_udmap 0xca02>,
		       <&main_udmap 0xca03>,
		       <&main_udmap 0xca04>,
		       <&main_udmap 0xca05>,
		       <&main_udmap 0xca06>,
		       <&main_udmap 0xca07>,
		       <&main_udmap 0x4a00>;
		dma-names = "tx0", "tx1", "tx2", "tx3",
			    "tx4", "tx5", "tx6", "tx7",
			    "rx";

		virt_emac_port {
			ti,label = "virt-port";
			/* local-mac-address = [0 0 0 0 0 0]; */
		};
	};

	evm_12v0: fixedregulator-evm12v0 {
		/* main supply */
		compatible = "regulator-fixed";
		regulator-name = "evm_12v0";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		regulator-always-on;
		regulator-boot-on;
	};

	vsys_3v3: fixedregulator-vsys3v3 {
		/* Output of LM5140 */
		compatible = "regulator-fixed";
		regulator-name = "vsys_3v3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&evm_12v0>;
		regulator-always-on;
		regulator-boot-on;
	};

	vsys_5v0: fixedregulator-vsys5v0 {
		/* Output of LM5140 */
		compatible = "regulator-fixed";
		regulator-name = "vsys_5v0";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&evm_12v0>;
		regulator-always-on;
		regulator-boot-on;
	};

	vdd_mmc1: fixedregulator-sd {
		/* Output of TPS22918 */
		compatible = "regulator-fixed";
		regulator-name = "vdd_mmc1";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		enable-active-high;
		vin-supply = <&vsys_3v3>;
		gpio = <&exp2 2 GPIO_ACTIVE_HIGH>;
	};

	vdd_sd_dv: gpio-regulator-TLV71033 {
		/* Output of TLV71033 */
		compatible = "regulator-gpio";
		regulator-name = "tlv71033";
		pinctrl-names = "default";
		pinctrl-0 = <&vdd_sd_dv_pins_default>;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <3300000>;
		regulator-boot-on;
		vin-supply = <&vsys_5v0>;
		gpios = <&main_gpio0 55 GPIO_ACTIVE_HIGH>;
		states = <1800000 0x0>,
			 <3300000 0x1>;
	};
};

&wkup_pmx2 {
	mcu_cpsw_pins_default: mcu-cpsw-pins-default {
		pinctrl-single,pins = <
			J721E_WKUP_IOPAD(0x0068, PIN_OUTPUT, 0) /* MCU_RGMII1_TX_CTL */
			J721E_WKUP_IOPAD(0x006c, PIN_INPUT, 0) /* MCU_RGMII1_RX_CTL */
			J721E_WKUP_IOPAD(0x0070, PIN_OUTPUT, 0) /* MCU_RGMII1_TD3 */
			J721E_WKUP_IOPAD(0x0074, PIN_OUTPUT, 0) /* MCU_RGMII1_TD2 */
			J721E_WKUP_IOPAD(0x0078, PIN_OUTPUT, 0) /* MCU_RGMII1_TD1 */
			J721E_WKUP_IOPAD(0x007c, PIN_OUTPUT, 0) /* MCU_RGMII1_TD0 */
			J721E_WKUP_IOPAD(0x0088, PIN_INPUT, 0) /* MCU_RGMII1_RD3 */
			J721E_WKUP_IOPAD(0x008c, PIN_INPUT, 0) /* MCU_RGMII1_RD2 */
			J721E_WKUP_IOPAD(0x0090, PIN_INPUT, 0) /* MCU_RGMII1_RD1 */
			J721E_WKUP_IOPAD(0x0094, PIN_INPUT, 0) /* MCU_RGMII1_RD0 */
			J721E_WKUP_IOPAD(0x0080, PIN_OUTPUT, 0) /* MCU_RGMII1_TXC */
			J721E_WKUP_IOPAD(0x0084, PIN_INPUT, 0) /* MCU_RGMII1_RXC */
		>;
	};

	mcu_mdio_pins_default: mcu-mdio1-pins-default {
		pinctrl-single,pins = <
			J721E_WKUP_IOPAD(0x009c, PIN_OUTPUT, 0) /* (L1) MCU_MDIO0_MDC */
			J721E_WKUP_IOPAD(0x0098, PIN_INPUT, 0) /* (L4) MCU_MDIO0_MDIO */
		>;
	};
};

&main_pmx0 {
	main_i2c0_pins_default: main-i2c0-pins-default {
		pinctrl-single,pins = <
			J721E_IOPAD(0xd4, PIN_INPUT_PULLUP, 0) /* (V3) I2C0_SCL */
			J721E_IOPAD(0xd8, PIN_INPUT_PULLUP, 0) /* (W2) I2C0_SDA */
		>;
	};

	main_i2c1_pins_default: main-i2c1-pins-default {
		pinctrl-single,pins = <
			J721E_IOPAD(0xdc, PIN_INPUT_PULLUP, 3) /* (U3) ECAP0_IN_APWM_OUT.I2C1_SCL */
			J721E_IOPAD(0xe0, PIN_INPUT_PULLUP, 3) /* (T3) EXT_REFCLK1.I2C1_SDA */
		>;
	};

	main_mmc1_pins_default: main-mmc1-pins-default {
		pinctrl-single,pins = <
			J721E_IOPAD(0x104, PIN_INPUT, 0) /* (M20) MMC1_CMD */
			J721E_IOPAD(0x100, PIN_INPUT, 0) /* (P21) MMC1_CLK */
			J721E_IOPAD(0xfc, PIN_INPUT, 0) /* (P25) MMC1_CLKLB */
			J721E_IOPAD(0xf8, PIN_INPUT, 0) /* (M19) MMC1_DAT0 */
			J721E_IOPAD(0xf4, PIN_INPUT, 0) /* (N21) MMC1_DAT1 */
			J721E_IOPAD(0xf0, PIN_INPUT, 0) /* (N20) MMC1_DAT2 */
			J721E_IOPAD(0xec, PIN_INPUT, 0) /* (N19) MMC1_DAT3 */
			J721E_IOPAD(0xe4, PIN_INPUT, 8) /* (V1) TIMER_IO0.MMC1_SDCD */
		>;
	};

	vdd_sd_dv_pins_default: vdd-sd-dv-pins-default {
		pinctrl-single,pins = <
			J721E_IOPAD(0xd0, PIN_OUTPUT, 7) /* (T5) SPI0_D1.GPIO0_55 */
		>;
	};
};

&main_pmx1 {
	main_usbss0_pins_default: main-usbss0-pins-default {
		pinctrl-single,pins = <
			J721E_IOPAD(0x04, PIN_OUTPUT, 0) /* (T4) USB0_DRVVBUS */
		>;
	};
};

&wkup_uart0 {
	/* Wakeup UART is used by System firmware */
	status = "reserved";
};

&main_uart0 {
	/* Shared with ATF on this platform */
	power-domains = <&k3_pds 146 TI_SCI_PD_SHARED>;
};

&main_uart2 {
	/* MAIN UART 2 is used by R5F firmware */
	status = "reserved";
};

&main_uart3 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart4 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart5 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart6 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart7 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart8 {
	/* UART not brought out */
	status = "disabled";
};

&main_uart9 {
	/* UART not brought out */
	status = "disabled";
};

&main_gpio2 {
	status = "disabled";
};

&main_gpio4 {
	status = "disabled";
};

&main_gpio6 {
	status = "disabled";
};

&wkup_gpio1 {
	status = "disabled";
};

&main_spi4 {
	status = "disabled";
};

&mcu_spi0 {
	status = "disabled";
};

&mcu_spi1 {
	status = "disabled";
};

&mcu_spi2 {
	status = "disabled";
};

&mcu_cpsw {
	pinctrl-names = "default";
	pinctrl-0 = <&mcu_cpsw_pins_default &mcu_mdio_pins_default>;
};

&davinci_mdio {
	bus_freq = <20000>;

	phy0: ethernet-phy@0 {
		reg = <0>;
		ti,rx-internal-delay = <DP83867_RGMIIDCTL_2_00_NS>;
		ti,fifo-depth = <DP83867_PHYCR_FIFO_DEPTH_4_B_NIB>;
	};
};

&cpsw_port1 {
	phy-mode = "rgmii-rxid";
	phy-handle = <&phy0>;
};

&main_i2c0 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_i2c0_pins_default>;
	clock-frequency = <400000>;

	exp1: gpio@20 {
		compatible = "ti,tca6416";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	exp2: gpio@22 {
		compatible = "ti,tca6424";
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
	};
};

/*
 * The j7200 CPB board is identical to the CPB used for J721E, the SOMs can be
 * swapped on the CPB.
 *
 * main_i2c1 of J7200 is connected to the CPB i2c bus labeled as i2c3.
 * The i2c1 of the CPB (as it is labeled) is not connected to j7200.
 */
&main_i2c1 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_i2c1_pins_default>;
	clock-frequency = <400000>;

	exp3: gpio@20 {
		compatible = "ti,tca6408";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
		gpio-line-names = "CODEC_RSTz", "CODEC_SPARE1", "UB926_RESETn",
				  "UB926_LOCK", "UB926_PWR_SW_CNTRL",
				  "UB926_TUNER_RESET", "UB926_GPIO_SPARE", "";
	};
};

&main_sdhci0 {
	/* eMMC */
	non-removable;
	ti,driver-strength-ohm = <50>;
	disable-wp;
};

&main_sdhci1 {
	/* SD card */
	pinctrl-0 = <&main_mmc1_pins_default>;
	pinctrl-names = "default";
	vmmc-supply = <&vdd_mmc1>;
	vqmmc-supply = <&vdd_sd_dv>;
	ti,driver-strength-ohm = <50>;
	disable-wp;
};

&serdes_ln_ctrl {
	idle-states = <J7200_SERDES0_LANE0_PCIE1_LANE0>, <J7200_SERDES0_LANE1_PCIE1_LANE1>,
		      <J7200_SERDES0_LANE2_QSGMII_LANE1>, <J7200_SERDES0_LANE3_IP4_UNUSED>;
};

&usb_serdes_mux {
	idle-states = <1>; /* USB0 to SERDES lane 3 */
};

&usbss0 {
	pinctrl-names = "default";
	pinctrl-0 = <&main_usbss0_pins_default>;
	ti,vbus-divider;
	ti,usb2-only;
};

&usb0 {
	dr_mode = "otg";
	maximum-speed = "high-speed";
};

&tscadc0 {
	adc {
		ti,adc-channels = <0 1 2 3 4 5 6 7>;
	};
};

&serdes_refclk {
	clock-frequency = <100000000>;
};

&serdes0 {
	serdes0_pcie_link: phy@0 {
		reg = <0>;
		cdns,num-lanes = <2>;
		#phy-cells = <0>;
		cdns,phy-type = <PHY_TYPE_PCIE>;
		resets = <&serdes_wiz0 1>, <&serdes_wiz0 2>;
	};

	serdes0_qsgmii_link: phy@1 {
		reg = <2>;
		cdns,num-lanes = <1>;
		#phy-cells = <0>;
		cdns,phy-type = <PHY_TYPE_QSGMII>;
		resets = <&serdes_wiz0 3>;
	};
};

&cpsw0 {
	/* Disable cpsw0 since cpsw5g_virt_mac is the default Ethernet
	 * controller. cpsw0 is enabled with overlay for native
	 * Ethernet driver support
	 */
	status = "disabled";
};

&pcie1_rc {
	reset-gpios = <&exp1 2 GPIO_ACTIVE_HIGH>;
	phys = <&serdes0_pcie_link>;
	phy-names = "pcie-phy";
	num-lanes = <2>;
};

&pcie1_ep {
	phys = <&serdes0_pcie_link>;
	phy-names = "pcie-phy";
	num-lanes = <2>;
	status = "disabled";
};

&wkup_i2c0 {
	status = "okay";
	tps6594x: tps6594x@48 {
		compatible = "ti,tps6594x";
		reg = <0x48>;
		ti,system-power-controller;

		rtc {
			compatible = "ti,tps6594x-rtc";
		};

		gpio {
			compatible = "ti,tps6594x-gpio";
		};
	};
};
