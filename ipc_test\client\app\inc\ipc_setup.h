/*
 *  \file ipc_setup.h
 *
 *  \brief Define the macros and functions for common IPC test
 *
 */

#ifndef IPC_SETUP_H_
#define IPC_SETUP_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <ti/csl/tistdtypes.h>
#define App_printf printf

/* this should be >= RPMessage_getObjMemRequired() */
#define IPC_RPMESSAGE_OBJ_SIZE  256U

/* this should be >= RPMessage_getMessageBufferSize() */
#define IPC_RPMESSAGE_MSG_BUFFER_SIZE  (496U + 32U)

#define RPMSG_DATA_SIZE         (256U*IPC_RPMESSAGE_MSG_BUFFER_SIZE + IPC_RPMESSAGE_OBJ_SIZE)
#define VQ_BUF_SIZE             2048U

/* Vring start address for each device */
#ifdef SOC_AM65XX
#define VRING_BASE_ADDRESS      0xA2000000U
#elif defined (SOC_J7200)
#define VRING_BASE_ADDRESS      0xA4000000U
#elif defined (SOC_AM64X)
#define VRING_BASE_ADDRESS      0xA5000000U
#elif defined (SOC_J721S2)
#define VRING_BASE_ADDRESS      0xA8000000U
#elif defined (SOC_J784S4) || defined (SOC_J742S2)
#define VRING_BASE_ADDRESS      0xAC000000U
#else
#define VRING_BASE_ADDRESS      0xAA000000U
#endif

int32_t Ipc_echo_test(void);

#ifdef __cplusplus
}
#endif

#endif /* IPC_SETUP_H_ */
