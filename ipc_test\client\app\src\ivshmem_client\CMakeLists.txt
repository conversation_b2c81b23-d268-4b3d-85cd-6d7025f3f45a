cmake_minimum_required(VERSION 3.10)
project(ivshmem_client CXX)

set(IVSHMEM_CLI_SRCS
    ${CMAKE_CURRENT_SOURCE_DIR}/ivshmem-client.c
)

# Loop through each file and set its language to CXX
foreach(src_file ${IVSHMEM_CLI_SRCS})
    set_property(SOURCE ${src_file} PROPERTY LANGUAGE CXX)
endforeach()

add_library(ivshmem_client STATIC
    ${IVSHMEM_CLI_SRCS}
)

# Set compile definitions
target_compile_definitions(ivshmem_client PRIVATE
    SOC_J784S4
)

# Set include directories
target_include_directories(ivshmem_client PRIVATE
    ${COM_DIR}
    ${QEMU_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/client/app/inc
    ${TI_PACKAGE_DIR}
    /usr/include/glib-2.0
    /usr/lib/x86_64-linux-gnu/glib-2.0/include
    /usr/lib/glib-2.0/include
)

# Link libraries
target_link_libraries(ivshmem_client
    pthread
    rt
    glib-2.0
)

set(IVSHMEM_CLI_TEST_SRCS
    ${CMAKE_CURRENT_SOURCE_DIR}/main-test.c
)

# Loop through each file and set its language to CXX
foreach(src_file ${IVSHMEM_CLI_TEST_SRCS})
    set_property(SOURCE ${src_file} PROPERTY LANGUAGE CXX)
endforeach()

# Define the ivshmem_client_test executable
add_executable(ivshmem_client_test
    ${IVSHMEM_CLI_TEST_SRCS}
)

# Set compile definitions
target_compile_definitions(ivshmem_client_test PRIVATE
    SOC_J784S4
)

# Set include directories
target_include_directories(ivshmem_client_test PRIVATE
    ${COM_DIR}
    ${QEMU_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/client/app/inc
    ${TI_PACKAGE_DIR}
    /usr/include/glib-2.0
    /usr/lib/x86_64-linux-gnu/glib-2.0/include
    /usr/lib/glib-2.0/include
)

# Link libraries
target_link_libraries(ivshmem_client_test
    pthread
    rt
    glib-2.0
    ivshmem_client
)

install(TARGETS ivshmem_client_test DESTINATION bin)
