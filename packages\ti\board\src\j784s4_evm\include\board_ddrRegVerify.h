/* Copyright (c) 2024, Texas Instruments Incorporated
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. */

/** \file board_ddrRegVerify.h
 *
 *   \brief This file contains DDR config values for register verification
 */

#ifndef BOARD_DDR_REG_VERIFY_H_
#define BOARD_DDR_REG_VERIFY_H_

#ifdef __cplusplus
extern "C" {
#endif

#define DDRSS_CTL_REG_VERIFY_COUNT (357U)
#define DDRSS_PHY_INDEP_REG_VERIFY_COUNT (246U)
#define DDRSS_PHY_REG_VERIFY_COUNT (299U)

uint32_t DDRSS_ctlRegRef[] = {
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002af8U,
    0x0001adafU,
    0x00000005U,
    0x0000006eU,
    0x000681c8U,
    0x004111c9U,
    0x00000005U,
    0x000010a9U,
    0x000681c8U,
    0x004111c9U,
    0x00000005U,
    0x000010a9U,
    0x01010000U,
    0x02011001U,
    0x02010000U,
    0x00020100U,
    0x0000000bU,
    0x0000001cU,
    0x00000000U,
    0x00000000U,
    0x03020200U,
    0x00005656U,
    0x00100000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x040c0000U,
    0x12481248U,
    0x00050804U,
    0x09040008U,
    0x15000204U,
    0x1760008bU,
    0x1500422bU,
    0x1760008bU,
    0x2000422bU,
    0x000a0a09U,
    0x040003c5U,
    0x1e161104U,
    0x1000922cU,
    0x1e161110U,
    0x1000922cU,
    0x02030410U,
    0x2c040500U,
    0x08292c29U,
    0x14000e0aU,
    0x04010a0aU,
    0x01010004U,
    0x04313104U,
    0x00003131U,
    0x03010000U,
    0x00001508U,
    0x00000063U,
    0x00001035U,
    0x0000032bU,
    0x00001035U,
    0x00050000U,
    0x00cb0012U,
    0x00cb0408U,
    0x00400408U,
    0x00120103U,
    0x00100005U,
    0x0505012fU,
    0x041e100bU,
    0x100b0401U,
    0x0001041eU,
    0x00160016U,
    0x033b033bU,
    0x033b033bU,
    0x03050505U,
    0x03010303U,
    0x200b100bU,
    0x04041004U,
    0x200b100bU,
    0x04041004U,
    0x03010000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x80104002U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x00050000U,
    0x00000004U,
    0x00000000U,
    0x00040005U,
    0x00000000U,
    0x000018c0U,
    0x000018c0U,
    0x000018c0U,
    0x000018c0U,
    0x000018c0U,
    0x000002b5U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00000000U,
    0x00007173U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00040d40U,
    0x00000000U,
    0x00007173U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0b030500U,
    0x00040b04U,
    0x0a090000U,
    0x0a090701U,
    0x0900000eU,
    0x0907010aU,
    0x00000e0aU,
    0x07010a09U,
    0x000e0a09U,
    0x07000401U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x08080000U,
    0x01000000U,
    0x800000c0U,
    0x800000c0U,
    0x800000c0U,
    0x00000000U,
    0x00001500U,
    0x00000000U,
    0x00000001U,
    0x00000002U,
    0x000e0006U,
    0x000e0404U,
    0x10100216U,
    0x01ab0216U,
    0x021600d6U,
    0x02161010U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3ff40084U,
    0x33003ff4U,
    0x00003333U,
    0x35000000U,
    0x27270035U,
    0x0f0f0000U,
    0x16000000U,
    0x00000020U,
    0x00000000U,
    0x00000001U,
    0x01000000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x02000000U,
    0x01080101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x006403e8U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x15110000U,
    0x00040c18U,
    0xf000c000U,
    0x0000f000U,
    0x00000000U,
    0x00000000U,
    0xc0000000U,
    0xf000f000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0xf000c000U,
    0x0000f000U,
    0x00000000U,
    0x00000000U,
    0x00030000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000200U,
    0x00370040U,
    0x00020008U,
    0x00400100U,
    0x00400855U,
    0x01000200U,
    0x08550040U,
    0x00000040U,
    0x006b0003U,
    0x0100006bU,
    0x03030303U,
    0x00000000U,
    0x00000202U,
    0x00001fffU,
    0x3fff2000U,
    0x03ff0000U,
    0x000103ffU,
    0x0fff0b00U,
    0x01010001U,
    0x01010101U,
    0x01180101U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00040101U,
    0x04010100U,
    0x00000000U,
    0x00000000U,
    0x03030300U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00020201U,
    0x00010101U,
    0x050a0a03U,
    0x10081f1fU,
    0x00090310U,
    0x0b0c030fU,
    0x0c090006U,
    0x0100000cU,
    0x08040801U,
    0x00000004U,
    0x00010000U,
    0x00280d00U,
    0x00000001U,
    0x00030001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000556aaU,
    0x000aaaaaU,
    0x000aa955U,
    0x00055555U,
    0x000b3133U,
    0x0004cd33U,
    0x0004ceccU,
    0x000b32ccU,
    0x00010300U,
    0x03000100U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x3a3a1b00U,
    0x000a0000U,
    0x000000c6U,
    0x00000200U,
    0x00000200U,
    0x00000252U,
    0x000007bcU,
    0x00000204U,
    0x0000206aU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00014424U,
    0x00000e15U,
    0x0000206aU,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00000200U,
    0x00014424U,
    0x02020e15U,
    0x03030202U,
    0x0007001fU,
};

uint32_t DDRSS_phyIndepRegRef[] = {
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00640000U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000003U,
    0x00000103U,
    0x00000005U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00280a00U,
    0x00000000U,
    0x0f000000U,
    0x00003200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x000000aaU,
    0x00000055U,
    0x000000b5U,
    0x0000004aU,
    0x00000056U,
    0x000000a9U,
    0x000000a9U,
    0x000000b5U,
    0x00000000U,
    0x00000000U,
    0x0000001bU,
    0x000007d0U,
    0x00000300U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010101U,
    0x00000000U,
    0x00030000U,
    0x0f000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0a0a140aU,
    0x10020201U,
    0x01000404U,
    0x00000000U,
    0x00000000U,
    0x0002020fU,
    0x00340000U,
    0x00000000U,
    0x00000000U,
    0x0000ffffU,
    0x01000000U,
    0x00080000U,
    0x02000200U,
    0x01000100U,
    0x01000000U,
    0x02000200U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000001U,
    0x00000000U,
    0x0000aa00U,
    0x00000000U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000008U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x0000000aU,
    0x00000019U,
    0x00000100U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00010003U,
    0x02000101U,
    0x01030001U,
    0x00010400U,
    0x06000105U,
    0x01070001U,
    0x00000000U,
    0x00000000U,
    0x00010001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000401U,
    0x00000000U,
    0x00010000U,
    0x00000000U,
    0x2b2b0200U,
    0x00000034U,
    0x00000064U,
    0x00020064U,
    0x02000200U,
    0x48120c04U,
    0x0000032bU,
    0x00001035U,
    0x0000032bU,
    0x04001035U,
    0x01010404U,
    0x00001500U,
    0x00150015U,
    0x01000100U,
    0x00000100U,
    0x00000000U,
    0x15040000U,
    0x0e0e0215U,
    0x00040402U,
    0x00218049U,
    0x01000101U,
    0x0004000eU,
    0x00040216U,
    0x01000216U,
    0x000f000fU,
    0x02170100U,
    0x01000217U,
    0x02170217U,
    0x01013210U,
    0x0a070601U,
    0x1f130a0dU,
    0x1f130a14U,
    0x0000c014U,
    0x00c01000U,
    0x00c01000U,
    0x00021000U,
    0x0024000eU,
    0x00240216U,
    0x00110216U,
    0x32000056U,
    0x00000301U,
    0x005b0036U,
    0x03013212U,
    0x00003600U,
    0x3212005bU,
    0x09000001U,
    0x04010504U,
    0x04000364U,
    0x0a032001U,
    0x2c31110aU,
    0x00002918U,
    0x1e202008U,
    0x2c311116U,
    0x6000838eU,
    0x1e202008U,
    0x000007bcU,
    0x0000206aU,
    0x00014424U,
    0x0000206aU,
    0x00014424U,
    0x033b0016U,
    0x0303033bU,
    0x002af803U,
    0x0001adafU,
    0x00000005U,
    0x0000006eU,
    0x00000016U,
    0x000681c8U,
    0x0001adafU,
    0x00000005U,
    0x000010a9U,
    0x0000033bU,
    0x000681c8U,
    0x0001adafU,
    0x00000005U,
    0x000010a9U,
    0x0100033bU,
    0x00370040U,
    0x00010008U,
    0x08550040U,
    0x00010040U,
    0x08550040U,
    0x00000340U,
    0x006b006bU,
    0x00000055U,
    0x5a000000U,
    0x3c5a0000U,
    0x0c3c5a00U,
    0x080f0e0dU,
    0x000b0a09U,
    0x00030201U,
    0x01000000U,
    0x04020201U,
    0x00080804U,
    0x00000000U,
    0x00000000U,
    0x00330084U,
    0x00160000U,
    0x35333ff4U,
    0x35333ff4U,
    0x00330084U,
    0x00160000U,
    0x35333ff4U,
    0x35333ff4U,
    0x00330084U,
    0x00160000U,
    0x35333ff4U,
};

uint32_t DDRSS_phyRegRef[] = {
    0x000004f0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x00000200U,
    0x000800c0U,
    0x060100ccU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000aaaaU,
    0x00005555U,
    0x0000b5b5U,
    0x00004a4aU,
    0x00005656U,
    0x0000a9a9U,
    0x0000a9a9U,
    0x0000b5b5U,
    0x00000000U,
    0x00000000U,
    0x2a000000U,
    0x00000808U,
    0x0f000000U,
    0x00000f08U,
    0x10400000U,
    0x0c002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xaaaaaaaaU,
    0x55555555U,
    0xaaaaaaaaU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000001U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x10001000U,
    0x0c083e42U,
    0x000004f0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030000U,
    0x00010000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x00000200U,
    0x000800c0U,
    0x060100ccU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000aaaaU,
    0x00005555U,
    0x0000b5b5U,
    0x00004a4aU,
    0x00005656U,
    0x0000a9a9U,
    0x0000a9a9U,
    0x0000b5b5U,
    0x00000000U,
    0x00000000U,
    0x2a000000U,
    0x00000808U,
    0x0f000000U,
    0x00000f08U,
    0x10400000U,
    0x0c002006U,
    0x00000000U,
    0x00000000U,
    0x55555555U,
    0xaaaaaaaaU,
    0x55555555U,
    0xaaaaaaaaU,
    0x00005555U,
    0x01000100U,
    0x00800180U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000104U,
    0x00000120U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x10001000U,
    0x0c083e42U,
    0x000004f0U,
    0x00000000U,
    0x00030200U,
    0x00000000U,
    0x00000000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800c0U,
    0x060100ccU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000aaaaU,
    0x00005555U,
    0x0000b5b5U,
    0x00004a4aU,
    0x00005656U,
    0x0000a9a9U,
    0x0000a9a9U,
    0x0000b5b5U,
    0x00000000U,
    0x00000000U,
    0x2a000000U,
    0x00000808U,
    0x0f000000U,
    0x00000f08U,
    0x000004f0U,
    0x00000000U,
    0x00000000U,
    0x01030004U,
    0x01000000U,
    0x00000000U,
    0x00000000U,
    0x01000001U,
    0x00000200U,
    0x000800c0U,
    0x060100ccU,
    0x00030066U,
    0x00000000U,
    0x00000301U,
    0x0000aaaaU,
    0x0000b5b5U,
    0x00004a4aU,
    0x00005656U,
    0x0000a9a9U,
    0x0000a9a9U,
    0x0000b5b5U,
    0x00000000U,
    0x00000000U,
    0x2a000000U,
    0x00000808U,
    0x0f000000U,
    0x00000f08U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00400000U,
    0x00000080U,
    0x00000000U,
    0x00000015U,
    0x00000015U,
    0x0000000cU,
    0x000f013fU,
    0x00000055U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00002001U,
    0x0000400fU,
    0x50020028U,
    0x01010000U,
    0x80080001U,
    0x10200000U,
    0x00000000U,
    0x01090e00U,
    0x00040101U,
    0x0000010fU,
    0x00000000U,
    0x00000064U,
    0x00000000U,
    0x01200f02U,
    0x00194280U,
    0x00000004U,
    0x00042000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x01000000U,
    0x00000108U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x03000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x04102006U,
    0x00041020U,
    0x01c98c98U,
    0x00010000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x76543210U,
    0x00000000U,
    0x00000000U,
    0x00040700U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000000U,
    0x00000002U,
    0x00000000U,
    0x00000000U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x51517041U,
    0x00c0c001U,
    0x0e0d0001U,
    0x0f0c3701U,
    0x01000140U,
    0x0c000420U,
    0x00000198U,
    0x0a0000d0U,
    0x00021010U,
    0x00100010U,
    0x00100010U,
    0x00100010U,
    0x51517041U,
    0x00c0c001U,
    0x0e0d0001U,
    0x0f0c3701U,
    0x01000140U,
    0x0c000420U,
    0x00000198U,
    0x0a0000d0U,
    0x00005555U,
    0x00005555U,
    0x000305ccU,
    0x00000054U,
    0x00080000U,
};

uint16_t DDRSS_ctlRegNumRef[] = {
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    56,
    57,
    59,
    60,
    61,
    63,
    64,
    65,
    67,
    68,
    69,
    70,
    71,
    72,
    74,
    76,
    77,
    78,
    79,
    80,
    81,
    82,
    83,
    84,
    85,
    86,
    87,
    88,
    90,
    91,
    92,
    93,
    94,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    134,
    135,
    136,
    137,
    138,
    139,
    140,
    141,
    142,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    153,
    154,
    155,
    156,
    157,
    165,
    166,
    168,
    169,
    170,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    180,
    181,
    190,
    191,
    192,
    194,
    195,
    196,
    197,
    198,
    199,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    223,
    224,
    225,
    226,
    227,
    228,
    229,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    306,
    307,
    309,
    311,
    312,
    313,
    314,
    317,
    318,
    319,
    320,
    321,
    323,
    324,
    325,
    326,
    328,
    329,
    330,
    331,
    333,
    334,
    344,
    345,
    346,
    347,
    348,
    349,
    350,
    351,
    352,
    353,
    354,
    355,
    356,
    357,
    358,
    359,
    360,
    361,
    362,
    363,
    364,
    365,
    366,
    367,
    368,
    369,
    370,
    371,
    372,
    373,
    374,
    375,
    381,
    382,
    383,
    385,
    386,
    387,
    388,
    389,
    390,
    391,
    394,
    395,
    396,
    397,
    398,
    399,
    400,
    403,
    404,
    405,
    406,
    407,
    408,
    409,
    411,
    412,
    413,
    422
};

uint16_t DDRSS_phyIndepRegNumRef[] = {
    1,
    2,
    3,
    4,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    27,
    28,
    29,
    30,
    31,
    32,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    57,
    58,
    59,
    60,
    61,
    63,
    64,
    65,
    67,
    68,
    69,
    70,
    71,
    72,
    73,
    74,
    75,
    76,
    77,
    83,
    84,
    86,
    87,
    88,
    89,
    90,
    95,
    96,
    97,
    98,
    99,
    100,
    101,
    102,
    103,
    104,
    105,
    106,
    107,
    108,
    109,
    110,
    111,
    112,
    113,
    114,
    115,
    116,
    117,
    118,
    119,
    120,
    121,
    122,
    123,
    124,
    125,
    126,
    127,
    128,
    129,
    130,
    131,
    132,
    133,
    135,
    136,
    137,
    138,
    139,
    141,
    143,
    144,
    145,
    146,
    147,
    148,
    149,
    150,
    151,
    152,
    154,
    155,
    156,
    157,
    158,
    159,
    160,
    161,
    162,
    163,
    164,
    165,
    166,
    167,
    168,
    171,
    172,
    173,
    174,
    175,
    176,
    177,
    178,
    179,
    184,
    185,
    186,
    187,
    190,
    191,
    192,
    193,
    194,
    195,
    196,
    197,
    198,
    200,
    201,
    202,
    203,
    204,
    205,
    206,
    207,
    208,
    209,
    210,
    211,
    212,
    213,
    214,
    215,
    216,
    217,
    218,
    219,
    220,
    221,
    222,
    224,
    225,
    227,
    228,
    230,
    231,
    232,
    233,
    234,
    235,
    236,
    237,
    238,
    239,
    240,
    241,
    242,
    243,
    244,
    245,
    246,
    247,
    248,
    249,
    250,
    251,
    252,
    253,
    254,
    255,
    256,
    257,
    258,
    260,
    262,
    264,
    266,
    267,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    279,
    281,
    282,
    283,
    285,
    287,
    288,
    297
};

uint16_t DDRSS_phyRegNumRef[] = {
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    62,
    63,
    64,
    65,
    66,
    67,
    68,
    69,
    70,
    71,
    72,
    103,
    104,
    256,
    257,
    258,
    259,
    260,
    261,
    262,
    263,
    264,
    265,
    266,
    268,
    269,
    270,
    271,
    272,
    273,
    274,
    275,
    276,
    277,
    278,
    279,
    280,
    281,
    282,
    283,
    284,
    285,
    286,
    287,
    288,
    289,
    290,
    291,
    292,
    293,
    294,
    295,
    296,
    297,
    298,
    299,
    300,
    301,
    302,
    303,
    304,
    305,
    306,
    307,
    308,
    309,
    310,
    311,
    312,
    313,
    314,
    315,
    316,
    317,
    318,
    319,
    320,
    321,
    322,
    323,
    324,
    325,
    326,
    327,
    328,
    359,
    360,
    512,
    513,
    514,
    515,
    516,
    519,
    520,
    521,
    522,
    523,
    524,
    525,
    526,
    527,
    528,
    529,
    530,
    531,
    532,
    533,
    534,
    535,
    536,
    537,
    538,
    539,
    540,
    541,
    542,
    543,
    768,
    769,
    772,
    775,
    776,
    777,
    778,
    779,
    780,
    781,
    782,
    783,
    784,
    785,
    786,
    788,
    789,
    790,
    791,
    792,
    793,
    794,
    795,
    796,
    797,
    798,
    799,
    1031,
    1033,
    1034,
    1035,
    1036,
    1040,
    1046,
    1047,
    1051,
    1055,
    1286,
    1288,
    1289,
    1290,
    1291,
    1292,
    1293,
    1294,
    1295,
    1296,
    1298,
    1299,
    1300,
    1301,
    1302,
    1303,
    1304,
    1307,
    1308,
    1309,
    1310,
    1311,
    1312,
    1313,
    1314,
    1316,
    1317,
    1332,
    1333,
    1334,
    1335,
    1336,
    1337,
    1338,
    1339,
    1340,
    1341,
    1342,
    1343,
    1344,
    1351,
    1352,
    1353,
    1355,
    1356,
    1359,
    1360,
    1361,
    1362,
    1363,
    1364,
    1366,
    1367,
    1369,
    1370,
    92,
    93,
    95,
    96,
    98,
    101,
    102,
    105,
    106,
    107,
    108,
    109,
    348,
    349,
    351,
    352,
    354,
    357,
    358,
    361,
    362,
    363,
    364,
    365,
    552,
    808,
    1064,
    1319,
    1374
};

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* BOARD_DDR_REG_VERIFY_H_ */
