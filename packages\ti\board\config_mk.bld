/******************************************************************************
 * FILE PURPOSE: Build configuration Script for the board library
 ******************************************************************************
 * FILE NAME: config.bld
 *
 * DESCRIPTION:
 *  This file contains the build configuration script for the board library
 *  and is responsible for configuration of the paths for the various
 *  tools required to build the library.
 *
 * Copyright (C) 2014-2015, Texas Instruments, Inc.
 *****************************************************************************/

/* Get the Tools Base directory from the Environment Variable. */
var c66ToolsBaseDir = java.lang.System.getenv("C6X_GEN_INSTALL_PATH");
var c674ToolsBaseDir = java.lang.System.getenv("C6X_GEN_INSTALL_PATH");
var m4ToolsBaseDir = java.lang.System.getenv("TOOLCHAIN_PATH_M4");
var a15ToolsBaseDir = java.lang.System.getenv("TOOLCHAIN_PATH_A15");
var a9ToolsBaseDir = java.lang.System.getenv("TOOLCHAIN_PATH_A9");
var arm9ToolsBaseDir = java.lang.System.getenv("TOOLCHAIN_PATH_ARM9");
var a8ToolsBaseDir = java.lang.System.getenv("TOOLCHAIN_PATH_A8");

/* Do not Print the Compiler Options */
var pOpts = 0;


/* List of all devices that needs to be build via XDC
 * As the build happens through makefile, there is nothing to build via XDC
 * using the below for packaging infrastructure
 */
var socs = [];
var devices = [];
var buildArguments = [];
var build_devices = [];
Build.useTargets = null;
Build.targets = []
var boards = [];

