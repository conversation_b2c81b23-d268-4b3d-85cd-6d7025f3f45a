/*
 * Copyright (C) 2023 Texas Instruments Incorporated - http://www.ti.com/
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the
 * distribution.
 *
 * Neither the name of Texas Instruments Incorporated nor the names of
 * its contributors may be used to endorse or promote products derived
 * from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#define QOS_0	(0 << 0)
#define QOS_1	(1 << 0)
#define QOS_2	(2 << 0)
#define QOS_3	(3 << 0)
#define QOS_4	(4 << 0)
#define QOS_5	(5 << 0)
#define QOS_6	(6 << 0)
#define QOS_7	(7 << 0)

#define ORDERID_0	(0 << 4)
#define ORDERID_1	(1 << 4)
#define ORDERID_2	(2 << 4)
#define ORDERID_3	(3 << 4)
#define ORDERID_4	(4 << 4)
#define ORDERID_5	(5 << 4)
#define ORDERID_6	(6 << 4)
#define ORDERID_7	(7 << 4)
#define ORDERID_8	(8 << 4)
#define ORDERID_9	(9 << 4)
#define ORDERID_10	(10 << 4)
#define ORDERID_11	(11 << 4)
#define ORDERID_12	(12 << 4)
#define ORDERID_13	(13 << 4)
#define ORDERID_14	(14 << 4)
#define ORDERID_15	(15 << 4)

#define ASEL_0	(0 << 8)
#define ASEL_1	(1 << 8)
#define ASEL_2	(2 << 8)
#define ASEL_3	(3 << 8)
#define ASEL_4	(4 << 8)
#define ASEL_5	(5 << 8)
#define ASEL_6	(6 << 8)
#define ASEL_7	(7 << 8)
#define ASEL_8	(8 << 8)
#define ASEL_9	(9 << 8)
#define ASEL_10	(10 << 8)
#define ASEL_11	(11 << 8)
#define ASEL_12	(12 << 8)
#define ASEL_13	(13 << 8)
#define ASEL_14	(14 << 8)
#define ASEL_15	(15 << 8)

#define EPRIORITY_0	(0 << 12)
#define EPRIORITY_1	(1 << 12)
#define EPRIORITY_2	(2 << 12)
#define EPRIORITY_3	(3 << 12)
#define EPRIORITY_4	(4 << 12)
#define EPRIORITY_5	(5 << 12)
#define EPRIORITY_6	(6 << 12)
#define EPRIORITY_7	(7 << 12)

#define VIRTID_0	(0 << 16)
#define VIRTID_1	(1 << 16)
#define VIRTID_2	(2 << 16)
#define VIRTID_3	(3 << 16)
#define VIRTID_4	(4 << 16)
#define VIRTID_5	(5 << 16)
#define VIRTID_6	(6 << 16)
#define VIRTID_7	(7 << 16)
#define VIRTID_8	(8 << 16)
#define VIRTID_9	(9 << 16)
#define VIRTID_10	(10 << 16)
#define VIRTID_11	(11 << 16)
#define VIRTID_12	(12 << 16)
#define VIRTID_13	(13 << 16)
#define VIRTID_14	(14 << 16)
#define VIRTID_15	(15 << 16)

#define ATYPE_0	(0 << 28)
#define ATYPE_1	(1 << 28)
#define ATYPE_2	(2 << 28)
#define ATYPE_3	(3 << 28)

#define NB_THREADMAP_0   ( 0 << 0 )
#define NB_THREADMAP_1   ( 1 << 0 )
#define NB_THREADMAP_2   ( 2 << 0 )
#define NB_THREADMAP_3   ( 3 << 0 )
#define NB_THREADMAP_4   ( 4 << 0 )
#define NB_THREADMAP_5   ( 5 << 0 )
#define NB_THREADMAP_6   ( 6 << 0 )
#define NB_THREADMAP_7   ( 7 << 0 )

#define SMS_WKUP_0_TIFS_VBUSP_M	0x45D00000
#define SMS_WKUP_0_HSM_VBUSP_M	0x45D00400
#define PULSAR_SL_MCU_0_CPU0_RMST	0x45D10000
#define PULSAR_SL_MCU_0_CPU0_WMST	0x45D10400
#define PULSAR_SL_MCU_0_CPU0_PMST	0x45D10800
#define PULSAR_SL_MCU_0_CPU1_RMST	0x45D11000
#define PULSAR_SL_MCU_0_CPU1_WMST	0x45D11400
#define PULSAR_SL_MCU_0_CPU1_PMST	0x45D11800
#define SA3SS_AM62_MCU_0_CTXCACH_EXT_DMA	0x45D13000
#define PULSAR_SL_MAIN_0_PBDG_RMST0	0x45D78000
#define PULSAR_SL_MAIN_0_PBDG_WMST0	0x45D78400
#define PULSAR_SL_MAIN_0_PBDG_RMST1	0x45D78800
#define PULSAR_SL_MAIN_0_PBDG_WMST1	0x45D78C00
#define EMMCSD4SS_MAIN_0_EMMCSDSS_RD	0x45D82800
#define EMMCSD4SS_MAIN_0_EMMCSDSS_WR	0x45D82C00
#define COMPUTE_CLUSTER_J7AHP_MAIN_0_GIC_MEM_RD_VBUSM	0x45D86000
#define COMPUTE_CLUSTER_J7AHP_MAIN_0_GIC_MEM_WR_VBUSM	0x45D86400
#define PCIE_G3X4_128_MAIN_0_PCIE_MST_RD	0x45D98400
#define PCIE_G3X4_128_MAIN_0_PCIE_MST_WR	0x45D98C00
#define PCIE_G3X4_128_MAIN_1_PCIE_MST_RD	0x45D99400
#define PCIE_G3X4_128_MAIN_1_PCIE_MST_WR	0x45D99C00
#define USB3P0SS_16FFC_MAIN_0_MSTR0	0x45D9A000
#define USB3P0SS_16FFC_MAIN_0_MSTW0	0x45D9A400
#define UFSHCI2P1SS_16FFC_MAIN_0_UFSHCI_VBM_MST_RD	0x45D9AC00
#define UFSHCI2P1SS_16FFC_MAIN_0_UFSHCI_VBM_MST_WR	0x45D9B000
#define EMMC8SS_16FFC_MAIN_0_EMMCSS_WR	0x45D9B400
#define EMMC8SS_16FFC_MAIN_0_EMMCSS_RD	0x45D9B800
#define SA2_UL_MAIN_0_CTXCACH_EXT_DMA	0x45D9BC00
#define VUSR_DUAL_MAIN_0_V0_M	0x45D9C000
#define VUSR_DUAL_MAIN_0_V1_M	0x45D9C400
#define PCIE_G3X4_128_MAIN_2_PCIE_MST_RD	0x45D9CC00
#define PCIE_G3X4_128_MAIN_3_PCIE_MST_WR	0x45D9D400
#define PCIE_G3X4_128_MAIN_2_PCIE_MST_WR	0x45D9D800
#define PCIE_G3X4_128_MAIN_3_PCIE_MST_RD	0x45D9DC00
#define DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMR	0x45DA0000
#define DEBUGSS_K3_WRAP_CV0_MAIN_0_VBUSMW	0x45DA0400
#define PULSAR_SL_MAIN_1_CPU0_RMST	0x45DA8000
#define PULSAR_SL_MAIN_1_CPU0_WMST	0x45DA8400
#define PULSAR_SL_MAIN_1_CPU1_RMST	0x45DA8800
#define PULSAR_SL_MAIN_1_CPU1_WMST	0x45DA8C00
#define PULSAR_SL_MAIN_2_CPU0_RMST	0x45DA9000
#define PULSAR_SL_MAIN_2_CPU0_WMST	0x45DA9400
#define PULSAR_SL_MAIN_2_CPU1_RMST	0x45DA9800
#define PULSAR_SL_MAIN_2_CPU1_WMST	0x45DA9C00
#define DMPAC_TOP_MAIN_0_DATA_MST	0x45DC0000
#define K3_VPU_WAVE521CL_MAIN_0_SEC_M_VBUSM_R_ASYNC	0x45DC0C00
#define K3_VPU_WAVE521CL_MAIN_0_SEC_M_VBUSM_W_ASYNC	0x45DC1000
#define VPAC_TOP_MAIN_0_DATA_MST_0	0x45DC1400
#define VPAC_TOP_MAIN_0_DATA_MST_1	0x45DC1800
#define VPAC_TOP_MAIN_0_LDC0_M_MST	0x45DC1C00
#define K3_DSS_MAIN_0_DSS_INST0_VBUSM_DMA	0x45DC2000
#define K3_DSS_MAIN_0_DSS_INST0_VBUSM_FBDC	0x45DC2400
#define VPAC_TOP_MAIN_1_LDC0_M_MST	0x45DC2800
#define VPAC_TOP_MAIN_1_DATA_MST_0	0x45DC2C00
#define VPAC_TOP_MAIN_1_DATA_MST_1	0x45DC3000
#define K3_VPU_WAVE521CL_MAIN_0_PRI_M_VBUSM_R_ASYNC	0x45DC3400
#define K3_VPU_WAVE521CL_MAIN_0_PRI_M_VBUSM_W_ASYNC	0x45DC3800
#define K3_VPU_WAVE521CL_MAIN_1_SEC_M_VBUSM_R_ASYNC	0x45DC3C00
#define K3_VPU_WAVE521CL_MAIN_1_SEC_M_VBUSM_W_ASYNC	0x45DC4000
#define K3_VPU_WAVE521CL_MAIN_1_PRI_M_VBUSM_R_ASYNC	0x45DC4400
#define K3_VPU_WAVE521CL_MAIN_1_PRI_M_VBUSM_W_ASYNC	0x45DC4800
#define J7AEP_GPU_BXS464_WRAP_MAIN_0_M_VBUSM_R_SYNC	0x45DC5000
#define J7AEP_GPU_BXS464_WRAP_MAIN_0_M_VBUSM_W_SYNC	0x45DC5800
#define PULSAR_SL_MAIN_0_CPU0_RMST	0x45DC8000
#define PULSAR_SL_MAIN_0_CPU0_WMST	0x45DC8400
#define PULSAR_SL_MAIN_0_CPU1_RMST	0x45DC8800
#define PULSAR_SL_MAIN_0_CPU1_WMST	0x45DC8C00
#define PULSAR_SL_MAIN_1_PBDG_RMST0	0x45DCA000
#define PULSAR_SL_MAIN_1_PBDG_WMST0	0x45DCA400
#define PULSAR_SL_MAIN_1_PBDG_RMST1	0x45DCA800
#define PULSAR_SL_MAIN_1_PBDG_WMST1	0x45DCAC00
#define PULSAR_SL_MAIN_2_PBDG_RMST0	0x45DCB000
#define PULSAR_SL_MAIN_2_PBDG_WMST0	0x45DCB400
#define PULSAR_SL_MAIN_2_PBDG_RMST1	0x45DCB800
#define PULSAR_SL_MAIN_2_PBDG_WMST1	0x45DCBC00
#define NAVSS0_NORTH_0_NBSS_NB0_CFG_MMRS    0x03702000
#define NAVSS0_NORTH_1_NBSS_NB1_CFG_MMRS    0x03703000
