Here is explaination for diagram
Use case: Initialization
1. Initialize ivshmem-server
1.1 Start ivshmem-server process.
1.2 Server initializes a shared memory on Host for exchanging VRING data between processes.
2. Initialize ivshmem-client
2.1 Start Core0 process.
2.2 Application call TI Driver API to initialize virtio.
2.3 IPC Driver initialize ivshmem-client
2.4 ivshmem-client connect to server. The server responds to the client with the information: clientID, other client fd, VRING shared memory fd

Use case: Send message
1. A application sends a message to a given destination (CPU, endpoint).
1.1 Application call RPMessage_send() API
2. The message is first copied from the application to VRING used between the two CPUs.
2.1 IPC Driver request ivshmem-client write message to VRING shared memory.
3. After this the IPC driver posts the VRING ID in the HW mailbox.
3.1 IPC Driver request ivshmem-client to kick (notify) other client with VRING ID
3.2 ivshmem-client notify corresponding client by file descriptor
4. This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING
4.1 ivshmem-client receives notification. It gets VRING ID then checks for any messages in that VRING
5. If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint
5.1 ivshmem-client reads message from VRING then puts it in the destination RPMSG endpoint queue
5.2 It then triggers the application blocked on this RPMSG endpoint
