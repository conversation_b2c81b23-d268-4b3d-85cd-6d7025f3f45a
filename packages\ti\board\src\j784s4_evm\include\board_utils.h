/******************************************************************************
 * Copyright (c) 2022 Texas Instruments Incorporated - http://www.ti.com
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions
 *  are met:
 *
 *    Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 *    Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the
 *    distribution.
 *
 *    Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 *  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 *  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 *  SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 *  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 *  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 *  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 *****************************************************************************/

/**
 *  \file   board_utils.h
 *
 *  \brief  Board utility functions header file
 *
 */

#ifndef _BOARD_UTILS_H_
#define _BOARD_UTILS_H_

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * Include Files                                                             *
 *****************************************************************************/
#include <ti/csl/csl_types.h>
#include <ti/csl/cslr_device.h>

#include <ti/board/board.h>
#include <ti/csl/tistdtypes.h>
#include <stdio.h>
#include <stdbool.h>

#define BOARD_PSC_DEVICE_MODE_EXCLUSIVE       (0)
#define BOARD_PSC_DEVICE_MODE_NONEXCLUSIVE    (1U)

#define BOARD_MAIN_CLOCK_GROUP_ALL            (0U)
#define BOARD_MAIN_CLOCK_GROUP1               (1U)
#define BOARD_MAIN_CLOCK_GROUP2               (2U)

#define BOARD_MCU_CLOCK_GROUP_ALL             (0U)
#define BOARD_MCU_CLOCK_GROUP1                (1U)
#define BOARD_MCU_CLOCK_GROUP2                (2U)


#define BOARD_MAC_ADDR_BYTES                  (6U)

#define BOARD_ENET_NONE                       ((int32_t)(0))
#define BOARD_ENET_QSGMII                     ((int32_t)(1))
#define BOARD_ENET_SGMII                      ((int32_t)(2))
#define BOARD_ENET_UNKOWN                     ((int32_t)(-1))

/* GPIO port and pin numbers for SDIO 1V8 enable */
#define BOARD_SDIO_1V8_EN_PIN_NUM             (8U) //GPIO0_8 - SEL_SDIO_3V3_1V8n


/**
 * \brief Structure to configure the board I2C parameters
 */
typedef struct Board_I2cInitCfg_s
{
    /** I2C controller instance */
    uint32_t i2cInst;
    /** SoC domain of the I2C controller */
    uint32_t socDomain;
    /** I2C controller interrupt enable/disable flag */
    bool enableIntr;
} Board_I2cInitCfg_t;

/**
 * \brief Structure to configure the board init parameters
 */
typedef struct Board_initParams_s
{
    /** UART controller instance */
    uint32_t uartInst;
    /** SoC domain of the UART controller */
    uint32_t uartSocDomain;
    /** Mode for PSC clock enable
        BOARD_PSC_DEVICE_MODE_EXCLUSIVE - Exclusive access to the core requesting access
        BOARD_PSC_DEVICE_MODE_NONEXCLUSIVE - Non-exclusive which allows other cores to get access */
    uint8_t pscMode;
    /** Group selection for MAIN domain clock enable
        BOARD_MAIN_CLOCK_GROUP_ALL - Enable clock for all groups in main domain
        BOARD_MAIN_CLOCK_GROUP1 - Enable clock for all group1 in main domain
        BOARD_MAIN_CLOCK_GROUP2 - Enable clock for all group2 in main domain */
    uint8_t mainClkGrp;
    /** Group selection for MCU domain clock enable
        BOARD_MCU_CLOCK_GROUP_ALL - Enable clock for all groups in mcu domain
        BOARD_MCU_CLOCK_GROUP1 - Enable clock for all group1 in mcu domain
        BOARD_MCU_CLOCK_GROUP2 - Enable clock for all group2 in mcu domain */
    uint8_t mcuClkGrp;
    /** Board ID of the ENET expansion board. Default - BOARD_ID_ENET */
    uint32_t enetBoardID;
    /** Flag to indicate whether both the ENET ports will be used for ENET card
        0 - One ENET port with enetBoardID is used for ENET card and other is used as USXGMII.
        1 - Both ENET ports are used for ENET card connection.
       */
    uint32_t dualEnetCfg;
} Board_initParams_t;

/**
 * \brief Structure to configure the board I2C parameters
 */
typedef struct Board_DetectCfg_s
{
    /** I2C controller instance */
    uint32_t i2cInst;
    /** Board ID EEPROM slave address */
    uint32_t slaveAddr;
    /** Board ID EEPROM I2C soc domain */
    uint8_t socDomain;
    /** Board name */
    char bName[20];
} Board_DetectCfg_t;

/**
 * \brief Board ID read function
 *
 * \param   info     [IN]  Board info structure
 * \param   boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_GESI(0x0) - GESI Board
 * \n                      BOARD_ID_FUSION2(0x1) - Fusion 2 Board
 * \n                      BOARD_ID_ENET(0x2) - ENET Board
 * \n                      BOARD_ID_EVM(0x3) - EVM Board
 *
 * \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_getBoardData(Board_IDInfo_v2 *info, uint32_t boardID);

/**
 * \brief Board detect function
 *
 *  Checks if the board with given 'boardID' is connected to the
 *  base board.
 *
 *  \n Note: Board ID EEPROM should be programmed for this function
 *           to work properly.
 *
 * \param   boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_GESI(0x0) - GESI Board
 * \n                      BOARD_ID_FUSION2(0x1) - Fusion 2 Board
 * \n                      BOARD_ID_ENET(0x2) - ENET Board
 * \n                      BOARD_ID_EVM(0x3) - EVM Board
 *
 * \return   BTRUE if the given board is detected else BFALSE.
 *           SoM board will be always connected to the base board.
 *           For SoM boardID return value BTRUE indicates dual PMIC
 *           SoM and BFALSE indicates alternate PMIC SoM
 *
 */
bool Board_detectBoard(uint32_t boardID);

/**
 * \brief  Checks for Alpha board revision
 *
 * \param   boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_GESI(0x0) - GESI Board
 * \n                      BOARD_ID_FUSION2(0x1) - Fusion 2 Board
 * \n                      BOARD_ID_ENET(0x2) - ENET Board
 * \n                      BOARD_ID_EVM(0x3) - EVM Board
 *
 * \return BTRUE if board revision is E2, BFALSE for all other cases
 */
bool Board_isAlpha(uint32_t boardID);

/**
 *  \brief    Function to detect ENET expansion application card type
 *
 * \param   boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_ENET(0x2) - ENET Board (ENET-EXP-1)
 * \n                      BOARD_ID_ENET2(0x4) - ENET board (ENET-EXP-2)
 *
 *  \return
 *            0 (BOARD_ENET_NONE)   - No board connected or invalid board ID data
 *            1 (BOARD_ENET_QSGMII) - QSGMII board connected
 *            2 (BOARD_ENET_SGMII)  - SGMII board connected
 *           -1 (BOARD_ENET_UNKOWN) - Unknown board
 */
int32_t Board_detectEnetCard(uint32_t enetExpId);

/**
 * \brief Read MAC ID function
 *
 *  This function reads the MAC addresses programmed to the board ID EEPROM
 *  on the boards with Ethernet ports. Exception is for MCU Ethernet port
 *  which is supposed to use MAC ID from SoC MMR registers.
 *
 *  There can be multiple MAC IDs stored in the EEPROM based on the
 *  number of Ethernet ports on the board. Number of MAC IDs copied
 *  to 'macAddrBuf' can be read using 'macAddrCnt' parameters.
 *
 *  Each MAC address will be 6 bytes long. MAC IDs will be copied to buffer
 *  based on 'macBufSize'. If the buffer size is long enough, all the MAC
 *  addresses from EEPROM will be copied to 'macAddrBuf' else fewer MAC
 *  IDs to fit within 'macBufSize'. MAC count for a given board can be
 *  read using Board_readMacAddrCount() function.
 *
 *  \n Note: Board ID EEPROM should be programmed for this function
 *           to work properly.
 *
 * \param  boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_GESI(0x0) - GESI Board
 * \n                      BOARD_ID_FUSION2(0x1) - Fusion 2 Board
 * \n                      BOARD_ID_ENET(0x2) - ENET Board
 * \n                      BOARD_ID_EVM(0x3) - EVM Board
 * \param  macAddrBuf[OUT] Buffer to write MAC IDs read from EEPROM
 * \param  macBufSize[IN]  Size of the macAddrBuf
 * \param  macAddrCnt[OUT] Number of valid MAC addresses programmed to the EEPROM
 *                         This an optional variable to read the MAC ID count
 *                         filled to the 'macAddrBuf'.
 *                         Pass a valid address to get MAC ID count.
 *
 * \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_readMacAddr(uint32_t boardID,
                               uint8_t  *macAddrBuf,
                               uint32_t macBufSize,
                               uint32_t *macAddrCnt);

/**
 * \brief Read MAC ID count
 *
 *  This function reads the number of MAC addresses programmed to
 *  board ID EEPROM on the boards with Ethernet ports. Exception is
 *  for MCU Ethernet port which is supposed to use MAC ID from SoC
 *  MMR registers. Each MAC address programmed to EEPROM is 6 bytes long.
 *
 *  \n Note: Board ID EEPROM should be programmed for this function
 *           to work properly.
 *
 * \param  boardID  [IN]  ID of the board to be detected
 * \n                      BOARD_ID_GESI(0x0) - GESI Board
 * \n                      BOARD_ID_FUSION2(0x1) - Fusion 2 Board
 * \n                      BOARD_ID_ENET(0x2) - ENET Board
 * \n                      BOARD_ID_EVM(0x3) - EVM Board
 * \param  macAddrCnt[OUT] Number of valid MAC addresses programmed to the EEPROM
 *
 * \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_readMacAddrCount(uint32_t boardID,
                                    uint32_t *macAddrCnt);

/**
 * \brief Function to configure I2C configurations used by board
 *
 * This function is used to set the I2C controller instance and
 * SoC domain used by the board module for IO expander and board
 * ID info read.
 *
 * Usage:
 * Call Board_setI2cInitConfig to set the I2C configurations
 * Call IO expander Init or Board ID info read/write
 *
 *  \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_setI2cInitConfig(Board_I2cInitCfg_t *i2cCfg);

/**
 * \brief Function to get board init params
 *
 *  This function shall be used to know the current board init
 *  parameters and update them if needed using the function Board_setInitParams.
 *
 * \param   initParams  [IN]  Board init params structure
 *
 * \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_getInitParams(Board_initParams_t *initParams);

/**
 * \brief Function to configure board init parameters
 *
 *  Board init params includes the parameters used by Board_init
 *  function for different operations. Default init parameters
 *  used by Board_init can be updated using this function.
 *  All the default params can be overwritten by calling this function
 *  or some of can be changed by reading the existing init parameters
 *  using Board_getInitParams function.
 *
 * Usage:
 * Call Board_getInitParams to get the default board init parameters
 * Update the parameters as needed
 * Call Board_setInitParams to update the default board init parameters
 *
 * \param   initCfg  [IN]  Board Init config structure
 *
 * \return   BOARD_SOK in case of success or appropriate error code.
 *
 */
Board_STATUS Board_setInitParams(Board_initParams_t *initParams);

/**
 * \brief Function to get the SoC domain
 *
 *  This function returns the domain of the SoC core on which
 *  it is executing.
 *
 * \return   SoC domain of the core.
 *
 */
uint32_t Board_getSocDomain(void);

/**
 *  \brief  Sets RAT configuration
 *
 *  MAIN padconfig registers are not directly accessible for C66x core
 *  which requires RAT configuration for the access.
 *
 *  \return   None
 */
void Board_setRATCfg(void);

/**
 *  \brief  Restores RAT configuration
 *
 *  \return   None
 */
void Board_restoreRATCfg(void);

/**
 *  \brief    Function to generate delay in micro seconds
 *
 *  This function takes the delay parameters in usecs but the generated
 *  delay will be in multiples of msecs due to the osal function which
 *  generates delay in msecs. Delay parameter passed will be converted to
 *  msecs and fractional value will be adjusted to nearest msecs value.
 *  Minimum delay generated by this function is 1 msec.
 *  Function parameter is kept in usecs to match with existing
 *  platforms which has delay function for usecs.
 *
 *  \param    usecs [IN]  Specifies the time to delay in micro seconds.
 *
 */
void BOARD_delay(uint32_t usecs);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* _BOARD_UTILS_H_ */
