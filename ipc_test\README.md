# IPC Baremetal Sanity Test

This project provides a CMake build system for the IPC baremetal sanity test. It uses the existing source files in the `ipc_test` directory without modifying any files in the `packages` folder.

## Directory Structure

```
ipc_test/
├── examples/
│   ├── baremetal/
│   │   └── ipc_baremetal_sanity_test.c - Baremetal sanity test implementation
│   └── common/
│       └── src/
│           ├── ipc_setup.h             - Common header file for IPC setup
│           ├── ipc_testsetup_baremetal.c - Implementation of IPC test setup
│           └── main_baremetail.c       - Main entry point
├── CMakeLists.txt                      - CMake configuration
├── build.sh                            - Build script
└── README.md                           - This file
```

## Building the Project

To build the project, simply run the build script:

```bash
./build.sh
```

This will create a `build` directory, configure the project with CMake, and build the executable.

## Running the Application

After building, you can run the application with:

```bash
./build/bin/ipc_baremetal_sanity_test
```

## Implementation Details

This project uses CMake to build the IPC baremetal sanity test. It includes:

1. The main entry point in `examples/common/src/main_baremetail.c`
2. The IPC test setup in `examples/common/src/ipc_testsetup_baremetal.c`
3. The baremetal sanity test implementation in `examples/baremetal/ipc_baremetal_sanity_test.c`

The CMake build system is configured to compile these files together into a single executable.
